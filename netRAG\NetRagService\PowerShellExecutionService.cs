using Microsoft.Extensions.Logging;
using System.Management.Automation;
using System.Management.Automation.Runspaces;
using System.Text.Json;

namespace NetRagService;

/// <summary>
/// Request model for PowerShell execution
/// </summary>
public class PowerShellExecutionRequest
{
    public string Command { get; set; } = string.Empty;
    public int TimeoutSeconds { get; set; } = 120;
    public Dictionary<string, object>? Parameters { get; set; }
}

/// <summary>
/// Response model for PowerShell execution
/// </summary>
public class PowerShellExecutionResponse
{
    public bool Success { get; set; }
    public string Output { get; set; } = string.Empty;
    public string Error { get; set; } = string.Empty;
    public int ExitCode { get; set; }
    public double ExecutionTimeMs { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Service for executing PowerShell commands using hosted PowerShell runtime
/// </summary>
public class PowerShellExecutionService
{
    private readonly ILogger<PowerShellExecutionService> _logger;
    private readonly Runspace _runspace;
    private readonly object _executionLock = new object();

    public PowerShellExecutionService(ILogger<PowerShellExecutionService> logger)
    {
        _logger = logger;
        
        // Create initial session state with Active Directory module
        var initialSessionState = InitialSessionState.CreateDefault();
        
        try
        {
            // Import Active Directory module if available
            initialSessionState.ImportPSModule(new[] { "ActiveDirectory" });
            _logger.LogInformation("Active Directory module imported successfully");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to import Active Directory module - commands may not work");
        }

        // Create runspace with the configured session state
        _runspace = RunspaceFactory.CreateRunspace(initialSessionState);
        _runspace.Open();
        
        _logger.LogInformation("PowerShell execution service initialized with hosted runspace");
    }

    /// <summary>
    /// Execute a PowerShell command
    /// </summary>
    /// <param name="request">Execution request</param>
    /// <returns>Execution response</returns>
    public async Task<PowerShellExecutionResponse> ExecuteCommandAsync(PowerShellExecutionRequest request)
    {
        var startTime = DateTime.UtcNow;
        var response = new PowerShellExecutionResponse();

        try
        {
            _logger.LogInformation("Executing PowerShell command: {Command}", request.Command);
            _logger.LogDebug("Command length: {Length}, Raw command: {RawCommand}",
                request.Command.Length, request.Command);

            // Use lock to ensure thread safety for the shared runspace
            await Task.Run(() =>
            {
                lock (_executionLock)
                {
                    using var powerShell = PowerShell.Create();
                    powerShell.Runspace = _runspace;

                    // Add the command as a script block
                    powerShell.AddScript(request.Command);

                    // Add parameters if provided
                    if (request.Parameters != null)
                    {
                        foreach (var param in request.Parameters)
                        {
                            powerShell.AddParameter(param.Key, param.Value);
                        }
                    }

                    // Execute with timeout
                    var asyncResult = powerShell.BeginInvoke();
                    var completed = asyncResult.AsyncWaitHandle.WaitOne(TimeSpan.FromSeconds(request.TimeoutSeconds));

                    if (!completed)
                    {
                        powerShell.Stop();
                        response.Success = false;
                        response.Error = $"Command timed out after {request.TimeoutSeconds} seconds";
                        response.ExitCode = -1;
                        return;
                    }

                    var results = powerShell.EndInvoke(asyncResult);

                    // Collect output
                    if (results != null && results.Count > 0)
                    {
                        var outputList = new List<object>();
                        foreach (var result in results)
                        {
                            if (result != null)
                            {
                                outputList.Add(result.BaseObject);
                            }
                        }

                        // Convert to JSON for consistent output format
                        response.Output = JsonSerializer.Serialize(outputList, new JsonSerializerOptions
                        {
                            WriteIndented = false,
                            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                        });
                    }

                    // Collect errors from all streams
                    var allErrors = new List<string>();

                    if (powerShell.Streams.Error.Count > 0)
                    {
                        allErrors.AddRange(powerShell.Streams.Error.Select(e => $"Error: {e}"));
                    }

                    if (powerShell.Streams.Warning.Count > 0)
                    {
                        allErrors.AddRange(powerShell.Streams.Warning.Select(w => $"Warning: {w}"));
                    }

                    if (powerShell.Streams.Verbose.Count > 0)
                    {
                        allErrors.AddRange(powerShell.Streams.Verbose.Select(v => $"Verbose: {v}"));
                    }

                    if (powerShell.Streams.Debug.Count > 0)
                    {
                        allErrors.AddRange(powerShell.Streams.Debug.Select(d => $"Debug: {d}"));
                    }

                    if (allErrors.Count > 0)
                    {
                        response.Error = string.Join(Environment.NewLine, allErrors);
                        response.Success = powerShell.Streams.Error.Count == 0; // Only fail on actual errors, not warnings
                        response.ExitCode = powerShell.Streams.Error.Count > 0 ? 1 : 0;

                        _logger.LogWarning("PowerShell execution had issues: {Errors}", response.Error);
                    }
                    else
                    {
                        response.Success = true;
                        response.ExitCode = 0;
                    }
                }
            });

            var endTime = DateTime.UtcNow;
            response.ExecutionTimeMs = (endTime - startTime).TotalMilliseconds;
            response.Timestamp = endTime;

            _logger.LogInformation("PowerShell command executed successfully in {ExecutionTime}ms", 
                response.ExecutionTimeMs);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to execute PowerShell command: {Command}", request.Command);
            
            var endTime = DateTime.UtcNow;
            response.Success = false;
            response.Error = ex.Message;
            response.ExitCode = -1;
            response.ExecutionTimeMs = (endTime - startTime).TotalMilliseconds;
            response.Timestamp = endTime;

            return response;
        }
    }

    /// <summary>
    /// Get information about the PowerShell execution environment
    /// </summary>
    /// <returns>Environment information</returns>
    public async Task<Dictionary<string, object>> GetEnvironmentInfoAsync()
    {
        try
        {
            var info = new Dictionary<string, object>();

            await Task.Run(() =>
            {
                lock (_executionLock)
                {
                    using var powerShell = PowerShell.Create();
                    powerShell.Runspace = _runspace;

                    // Get PowerShell version
                    powerShell.AddScript("$PSVersionTable.PSVersion.ToString()");
                    var versionResults = powerShell.Invoke();
                    if (versionResults.Count > 0)
                    {
                        info["powershell_version"] = versionResults[0].ToString();
                    }

                    powerShell.Commands.Clear();

                    // Check if Active Directory module is available
                    powerShell.AddScript("Get-Module -Name ActiveDirectory -ListAvailable | Select-Object -First 1 | Select-Object Name, Version");
                    var adModuleResults = powerShell.Invoke();
                    if (adModuleResults.Count > 0 && adModuleResults[0] != null)
                    {
                        info["active_directory_module"] = "available";
                        info["ad_module_info"] = adModuleResults[0].BaseObject;
                    }
                    else
                    {
                        info["active_directory_module"] = "not_available";
                    }

                    powerShell.Commands.Clear();

                    // Get execution policy
                    powerShell.AddScript("Get-ExecutionPolicy");
                    var policyResults = powerShell.Invoke();
                    if (policyResults.Count > 0)
                    {
                        info["execution_policy"] = policyResults[0].ToString();
                    }
                }
            });

            info["runspace_state"] = _runspace.RunspaceStateInfo.State.ToString();
            info["service_initialized"] = DateTime.UtcNow.ToString("O");

            return info;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get PowerShell environment information");
            return new Dictionary<string, object>
            {
                ["error"] = ex.Message,
                ["runspace_state"] = _runspace.RunspaceStateInfo.State.ToString()
            };
        }
    }

    /// <summary>
    /// Dispose of resources
    /// </summary>
    public void Dispose()
    {
        try
        {
            _runspace?.Close();
            _runspace?.Dispose();
            _logger.LogInformation("PowerShell execution service disposed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing PowerShell execution service");
        }
    }
}
