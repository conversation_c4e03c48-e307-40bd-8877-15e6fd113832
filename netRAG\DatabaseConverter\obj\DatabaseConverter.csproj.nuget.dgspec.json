{"format": 1, "restore": {"F:\\Code\\7253\\netRAG\\DatabaseConverter\\DatabaseConverter.csproj": {}}, "projects": {"F:\\Code\\7253\\netRAG\\DatabaseConverter\\DatabaseConverter.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Code\\7253\\netRAG\\DatabaseConverter\\DatabaseConverter.csproj", "projectName": "DatabaseConverter", "projectPath": "F:\\Code\\7253\\netRAG\\DatabaseConverter\\DatabaseConverter.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Code\\7253\\netRAG\\DatabaseConverter\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"F:\\Code\\7253\\netRAG\\NetRagService\\NetRagService.csproj": {"projectPath": "F:\\Code\\7253\\netRAG\\NetRagService\\NetRagService.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.SemanticKernel": {"target": "Package", "version": "[1.60.0, )"}, "Microsoft.SemanticKernel.Connectors.Onnx": {"target": "Package", "version": "[1.60.0-alpha, )"}, "System.Numerics.Tensors": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}, "F:\\Code\\7253\\netRAG\\NetRagService\\NetRagService.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Code\\7253\\netRAG\\NetRagService\\NetRagService.csproj", "projectName": "NetRagService", "projectPath": "F:\\Code\\7253\\netRAG\\NetRagService\\NetRagService.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Code\\7253\\netRAG\\NetRagService\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting.WindowsServices": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.EventLog": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[8.0.18, )", "autoReferenced": true}, "Microsoft.PowerShell.SDK": {"target": "Package", "version": "[7.4.6, )"}, "Microsoft.SemanticKernel": {"target": "Package", "version": "[1.60.0, )"}, "Microsoft.SemanticKernel.Connectors.Onnx": {"target": "Package", "version": "[1.60.0-alpha, )"}, "System.Numerics.Tensors": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[8.0.18, 8.0.18]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}