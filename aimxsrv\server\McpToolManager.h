/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    McpToolManager.h

Abstract:

    Header file for the MCP Tool Manager component that manages MCP tool execution lifecycle.
    Handles tool parameters, validation, execution timeouts, and result formatting.
    Provides both synchronous and asynchronous execution capabilities.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/11/2025

--*/

#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include "AimxCommon.h"
#include "nlohmann/json.hpp"
#include "McpSvrMgr.h"

// MCP tool execution status enumeration
enum class MCP_TOOL_EXECUTION_STATUS
{
    UNKNOWN = 0,
    PENDING,
    RUNNING,
    COMPLETED,
    FAILED,
    TIMEOUT,
    CANCELLED
};

// MCP tool execution result structure
struct MCP_TOOL_EXECUTION_RESULT
{
    MCP_TOOL_EXECUTION_STATUS status;
    nlohmann::json result;
    std::wstring errorMessage;
    DWORD executionTimeMs;
    FILETIME startTime;
    FILETIME endTime;
};

// MCP tool execution context for async operations
struct MCP_TOOL_EXECUTION_CONTEXT
{
    GUID operationId;
    std::wstring serverName;
    std::wstring toolName;
    nlohmann::json parameters;
    DWORD timeoutMs;
    HANDLE hThread;
    std::atomic<bool> cancelRequested;
    MCP_TOOL_EXECUTION_RESULT result;
};

// MCP tool execution configuration
struct MCP_TOOL_EXECUTION_CONFIG
{
    DWORD defaultTimeoutMs;
    DWORD maxConcurrentExecutions;
    bool enableRetry;
    DWORD retryCount;
    DWORD retryDelayMs;
    bool enableLogging;
};

// MCP Tool Manager component class
class McpToolManager
{
public:
    // Initialize the MCP Tool Manager component
    static HRESULT Initialize();

    // Uninitialize and cleanup resources
    static void Uninitialize();

    // Execute a tool synchronously
    static HRESULT ExecuteTool(
        _In_ const std::wstring& serverName,
        _In_ const std::wstring& toolName,
        _In_ const nlohmann::json& parameters,
        _Out_ MCP_TOOL_EXECUTION_RESULT& result
        );

    // Execute a tool synchronously with timeout
    static HRESULT ExecuteToolWithTimeout(
        _In_ const std::wstring& serverName,
        _In_ const std::wstring& toolName,
        _In_ const nlohmann::json& parameters,
        _In_ DWORD timeoutMs,
        _Out_ MCP_TOOL_EXECUTION_RESULT& result
        );

    // Tool identification and formatting utilities
    static std::wstring CreateToolId(
        _In_ const std::wstring& serverName,
        _In_ const std::wstring& toolName
        );

    static std::wstring CreateDisplayName(
        _In_ const std::wstring& serverName,
        _In_ const std::wstring& toolName
        );

    static bool ParseToolId(
        _In_ const std::wstring& toolId,
        _Out_ std::wstring& serverName,
        _Out_ std::wstring& toolName
        );

    static bool FindToolByName(
        _In_ const std::wstring& targetToolName,
        _In_ const std::vector<MCP_SERVER_INFO>& servers,
        _Out_ std::wstring& foundServerName,
        _Out_ MCP_TOOL_INFO& foundTool
        );

    // Get the singleton instance
    static McpToolManager* GetInstance();

private:
    // Private constructor for singleton pattern
    McpToolManager();
    
    // Private destructor
    ~McpToolManager();

    // Delete copy constructor and assignment operator
    McpToolManager(const McpToolManager&) = delete;
    McpToolManager& operator=(const McpToolManager&) = delete;

    // Internal execution methods
    HRESULT ExecuteToolInternal(
        _In_ const std::wstring& serverName,
        _In_ const std::wstring& toolName,
        _In_ const nlohmann::json& parameters,
        _In_ DWORD timeoutMs,
        _Out_ MCP_TOOL_EXECUTION_RESULT& result
        );

    // Validation and formatting methods
    HRESULT ValidateExecutionRequest(
        _In_ const std::wstring& serverName,
        _In_ const std::wstring& toolName,
        _In_ const nlohmann::json& parameters
        );

    HRESULT FormatExecutionResult(
        _In_ const nlohmann::json& rawResult,
        _Out_ MCP_TOOL_EXECUTION_RESULT& formattedResult
        );

    // Error handling methods
    HRESULT HandleExecutionError(
        _In_ HRESULT errorCode,
        _In_ const std::wstring& errorContext,
        _Out_ MCP_TOOL_EXECUTION_RESULT& result
        );

    // Direct PowerShell execution method
    HRESULT ExecutePowerShellToolDirect(
        _In_ const std::wstring& toolName,
        _In_ const nlohmann::json& parameters,
        _Out_ MCP_TOOL_EXECUTION_RESULT& result
        );

    // PowerShell server scope helper method
    std::wstring GetPowerShellServerScope(
        _In_ const std::wstring& command
        );

    // Internal data structures
    static McpToolManager* s_instance;
    static std::shared_mutex s_instanceMutex;
    
    MCP_TOOL_EXECUTION_CONFIG m_config;
    std::unordered_map<GUID, std::unique_ptr<MCP_TOOL_EXECUTION_CONTEXT>, GuidHash, GuidEqual> m_asyncExecutions;
    
    mutable std::shared_mutex m_executionMapMutex;
    mutable std::shared_mutex m_configMutex;
    
    // Statistics tracking
    std::atomic<DWORD> m_totalExecutions;
    std::atomic<DWORD> m_successfulExecutions;
    std::atomic<DWORD> m_failedExecutions;
    std::atomic<DWORD> m_timeoutExecutions;

    // PowerShell server scope caching
    std::wstring m_defaultPowerShellServer;
    mutable std::shared_mutex m_serverScopeMutex;

    bool m_initialized;
};
