{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {}, ".NETCoreApp,Version=v8.0/win-x64": {"NetRagService/1.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.WindowsServices": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Console": "8.0.0", "Microsoft.Extensions.Logging.EventLog": "8.0.0", "Microsoft.PowerShell.SDK": "7.4.6", "Microsoft.SemanticKernel": "1.60.0", "Microsoft.SemanticKernel.Connectors.Onnx": "1.60.0-alpha", "System.Numerics.Tensors": "9.0.6"}, "runtime": {"NetRagService.dll": {}}}, "Azure.AI.OpenAI/2.2.0-beta.4": {"dependencies": {"Azure.Core": "1.44.1", "OpenAI": "2.2.0-beta.4", "System.ClientModel": "1.4.0-beta.1"}, "runtime": {"lib/net8.0/Azure.AI.OpenAI.dll": {"assemblyVersion": "*******", "fileVersion": "2.200.25.16901"}}}, "Azure.Core/1.44.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.ClientModel": "1.4.0-beta.1", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Memory.Data": "8.0.1", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.44.1.0", "fileVersion": "1.4400.124.50905"}}}, "FastBertTokenizer/1.0.28": {"runtime": {"lib/net8.0/FastBertTokenizer.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.28.51251"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Json.More.Net/*******": {"runtime": {"lib/net8.0/Json.More.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "*******"}}}, "JsonPointer.Net/5.0.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Json.More.Net": "*******"}, "runtime": {"lib/net8.0/JsonPointer.Net.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.0"}}}, "JsonSchema.Net/7.0.4": {"dependencies": {"JsonPointer.Net": "5.0.0"}, "runtime": {"lib/net8.0/JsonSchema.Net.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.4.0"}}}, "Markdig.Signed/0.33.0": {"runtime": {"lib/net6.0/Markdig.Signed.dll": {"assemblyVersion": "0.33.0.0", "fileVersion": "0.33.0.0"}}}, "Microsoft.ApplicationInsights/2.21.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "8.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"assemblyVersion": "2.21.0.429", "fileVersion": "2.21.0.429"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Bcl.HashCode/1.1.1": {"runtime": {"lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "4.700.20.56604"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {}, "Microsoft.CodeAnalysis.Common/4.9.2": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "4.9.0.0", "fileVersion": "4.900.224.12906"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.9.2": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.9.2"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "4.9.0.0", "fileVersion": "4.900.224.12906"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Extensions.AI/9.6.0": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.6.0", "Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Text.Json": "8.0.5", "System.Threading.Channels": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.AI.dll": {"assemblyVersion": "9.6.0.0", "fileVersion": "9.600.25.31002"}}}, "Microsoft.Extensions.AI.Abstractions/9.6.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.AI.Abstractions.dll": {"assemblyVersion": "9.6.0.0", "fileVersion": "9.600.25.31002"}}}, "Microsoft.Extensions.AI.OpenAI/9.5.0-preview.1.25265.7": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.6.0", "OpenAI": "2.2.0-beta.4", "System.Memory.Data": "8.0.1", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.AI.OpenAI.dll": {"assemblyVersion": "9.5.0.0", "fileVersion": "9.500.25.26507"}}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.5"}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Diagnostics/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.1"}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {}, "Microsoft.Extensions.Hosting/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Logging.Console": "8.0.0", "Microsoft.Extensions.Logging.Debug": "8.0.0", "Microsoft.Extensions.Logging.EventLog": "8.0.0", "Microsoft.Extensions.Logging.EventSource": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3"}}, "Microsoft.Extensions.Hosting.WindowsServices/8.0.0": {"dependencies": {"Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Logging.EventLog": "8.0.0", "System.ServiceProcess.ServiceController": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.WindowsServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/8.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1325.6609"}}}, "Microsoft.Extensions.Logging.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Logging.Console/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Text.Json": "8.0.5"}}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3"}}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.EventLog": "8.0.1"}}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0", "System.Text.Json": "8.0.5"}}, "Microsoft.Extensions.ObjectPool/8.0.10": {"runtime": {"lib/net8.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46804"}}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.Extensions.VectorData.Abstractions/9.7.0": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.6.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Management.Infrastructure/3.0.0": {"dependencies": {"Microsoft.Management.Infrastructure.Runtime.Unix": "3.0.0", "Microsoft.Management.Infrastructure.Runtime.Win": "3.0.0"}}, "Microsoft.Management.Infrastructure.CimCmdlets/7.4.6": {"dependencies": {"System.Management.Automation": "7.4.6"}, "runtime": {"runtimes/win/lib/net8.0/Microsoft.Management.Infrastructure.CimCmdlets.dll": {"assemblyVersion": "7.4.6.500", "fileVersion": "7.4.6.500"}}}, "Microsoft.Management.Infrastructure.Runtime.Unix/3.0.0": {}, "Microsoft.Management.Infrastructure.Runtime.Win/3.0.0": {"runtime": {"runtimes/win-x64/lib/netstandard1.6/microsoft.management.infrastructure.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "10.0.17763.30000"}, "runtimes/win-x64/lib/netstandard1.6/microsoft.management.infrastructure.native.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "10.0.17763.30000"}}, "native": {"runtimes/win-x64/native/microsoft.management.infrastructure.native.unmanaged.dll": {"fileVersion": "6.3.9600.18144"}}}, "Microsoft.ML.OnnxRuntime/1.22.0": {"dependencies": {"Microsoft.ML.OnnxRuntime.Managed": "1.22.0"}, "native": {"runtimes/win-x64/native/onnxruntime.dll": {"fileVersion": "1.22.25.508"}, "runtimes/win-x64/native/onnxruntime.lib": {"fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/onnxruntime_providers_shared.dll": {"fileVersion": "1.22.25.508"}, "runtimes/win-x64/native/onnxruntime_providers_shared.lib": {"fileVersion": "0.0.0.0"}}}, "Microsoft.ML.OnnxRuntime.Managed/1.22.0": {"dependencies": {"System.Memory": "4.5.5", "System.Numerics.Tensors": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.ML.OnnxRuntime.dll": {"assemblyVersion": "1.22.0.0", "fileVersion": "1.22.0.0"}}}, "Microsoft.ML.OnnxRuntimeGenAI/0.8.2": {"dependencies": {"Microsoft.ML.OnnxRuntime": "1.22.0", "Microsoft.ML.OnnxRuntimeGenAI.Managed": "0.8.2"}, "native": {"runtimes/win-x64/native/onnxruntime-genai.dll": {"fileVersion": "0.8.2.0"}, "runtimes/win-x64/native/onnxruntime-genai.lib": {"fileVersion": "0.0.0.0"}}}, "Microsoft.ML.OnnxRuntimeGenAI.Managed/0.8.2": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.6.0"}, "runtime": {"lib/net8.0/Microsoft.ML.OnnxRuntimeGenAI.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Microsoft.PowerShell.Commands.Diagnostics/7.4.6": {"dependencies": {"System.Management.Automation": "7.4.6"}, "runtime": {"runtimes/win/lib/net8.0/Microsoft.PowerShell.Commands.Diagnostics.dll": {"assemblyVersion": "7.4.6.500", "fileVersion": "7.4.6.500"}}}, "Microsoft.PowerShell.Commands.Management/7.4.6": {"dependencies": {"Microsoft.PowerShell.Security": "7.4.6", "System.ServiceProcess.ServiceController": "8.0.1"}, "runtime": {"runtimes/win/lib/net8.0/Microsoft.PowerShell.Commands.Management.dll": {"assemblyVersion": "7.4.6.500", "fileVersion": "7.4.6.500"}}}, "Microsoft.PowerShell.Commands.Utility/7.4.6": {"dependencies": {"JsonSchema.Net": "7.0.4", "Markdig.Signed": "0.33.0", "Microsoft.CodeAnalysis.CSharp": "4.9.2", "Microsoft.PowerShell.MarkdownRender": "7.2.1", "System.Drawing.Common": "9.0.0-preview.6.24327.6", "System.Management.Automation": "7.4.6", "System.Threading.AccessControl": "9.0.0-preview.6.24327.7"}, "runtime": {"runtimes/win/lib/net8.0/Microsoft.PowerShell.Commands.Utility.dll": {"assemblyVersion": "7.4.6.500", "fileVersion": "7.4.6.500"}}}, "Microsoft.PowerShell.ConsoleHost/7.4.6": {"dependencies": {"System.Management.Automation": "7.4.6"}, "runtime": {"runtimes/win/lib/net8.0/Microsoft.PowerShell.ConsoleHost.dll": {"assemblyVersion": "7.4.6.500", "fileVersion": "7.4.6.500"}}}, "Microsoft.PowerShell.CoreCLR.Eventing/7.4.6": {"dependencies": {"System.Diagnostics.EventLog": "8.0.1"}, "runtime": {"runtimes/win/lib/net8.0/Microsoft.PowerShell.CoreCLR.Eventing.dll": {"assemblyVersion": "7.4.6.500", "fileVersion": "7.4.6.500"}}}, "Microsoft.PowerShell.MarkdownRender/7.2.1": {"dependencies": {"Markdig.Signed": "0.33.0"}, "runtime": {"lib/netstandard2.0/Microsoft.PowerShell.MarkdownRender.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.PowerShell.Native/7.4.0": {"native": {"runtimes/win-x64/native/PowerShell.Core.Instrumentation.dll": {"fileVersion": "10.0.10011.16384"}, "runtimes/win-x64/native/pwrshplugin.dll": {"fileVersion": "10.0.10011.16384"}}}, "Microsoft.PowerShell.SDK/7.4.6": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.ObjectPool": "8.0.10", "Microsoft.Management.Infrastructure.CimCmdlets": "7.4.6", "Microsoft.PowerShell.Commands.Diagnostics": "7.4.6", "Microsoft.PowerShell.Commands.Management": "7.4.6", "Microsoft.PowerShell.Commands.Utility": "7.4.6", "Microsoft.PowerShell.ConsoleHost": "7.4.6", "Microsoft.PowerShell.Security": "7.4.6", "Microsoft.WSMan.Management": "7.4.6", "Microsoft.Windows.Compatibility": "8.0.10", "System.Data.SqlClient": "4.8.6", "System.IO.Packaging": "8.0.1", "System.Management.Automation": "7.4.6", "System.Net.Http.WinHttpHandler": "8.0.2", "System.Private.ServiceModel": "4.10.3", "System.Runtime.Caching": "8.0.1", "System.ServiceModel.Duplex": "4.10.3", "System.ServiceModel.Http": "4.10.3", "System.ServiceModel.NetTcp": "4.10.3", "System.ServiceModel.Primitives": "4.10.3", "System.ServiceModel.Security": "4.10.3", "System.Text.Encodings.Web": "8.0.0", "System.Web.Services.Description": "4.10.3"}, "runtime": {"runtimes/win/lib/net8.0/Microsoft.PowerShell.SDK.dll": {"assemblyVersion": "7.4.6.500", "fileVersion": "7.4.6.500"}}}, "Microsoft.PowerShell.Security/7.4.6": {"dependencies": {"System.Management.Automation": "7.4.6"}, "runtime": {"runtimes/win/lib/net8.0/Microsoft.PowerShell.Security.dll": {"assemblyVersion": "7.4.6.500", "fileVersion": "7.4.6.500"}}}, "Microsoft.Security.Extensions/1.2.0": {"runtime": {"runtimes/win-x64/lib/net5.0/getfilesiginforedistwrapper.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25098.1000"}}, "native": {"runtimes/win-x64/native/getfilesiginforedist.dll": {"fileVersion": "0.0.0.0"}}}, "Microsoft.SemanticKernel/1.60.0": {"dependencies": {"Microsoft.SemanticKernel.Connectors.AzureOpenAI": "1.60.0", "Microsoft.SemanticKernel.Core": "1.60.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.SemanticKernel.Abstractions/1.60.0": {"dependencies": {"Microsoft.Bcl.HashCode": "1.1.1", "Microsoft.Extensions.AI": "9.6.0", "Microsoft.Extensions.VectorData.Abstractions": "9.7.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.SemanticKernel.Connectors.AzureOpenAI/1.60.0": {"dependencies": {"Azure.AI.OpenAI": "2.2.0-beta.4", "Microsoft.SemanticKernel.Connectors.OpenAI": "1.60.0", "Microsoft.SemanticKernel.Core": "1.60.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.AzureOpenAI.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.SemanticKernel.Connectors.Onnx/1.60.0-alpha": {"dependencies": {"FastBertTokenizer": "1.0.28", "Microsoft.ML.OnnxRuntime": "1.22.0", "Microsoft.ML.OnnxRuntimeGenAI": "0.8.2", "Microsoft.SemanticKernel.Core": "1.60.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.Onnx.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.SemanticKernel.Connectors.OpenAI/1.60.0": {"dependencies": {"Microsoft.Extensions.AI.OpenAI": "9.5.0-preview.1.25265.7", "Microsoft.SemanticKernel.Core": "1.60.0", "OpenAI": "2.2.0-beta.4"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.OpenAI.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.SemanticKernel.Core/1.60.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.SemanticKernel.Abstractions": "1.60.0", "System.Numerics.Tensors": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "6.0.1", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.Registry.AccessControl/8.0.0": {"runtime": {"runtimes/win/lib/net8.0/Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Win32.SystemEvents/9.0.0-preview.6.24327.7": {"runtime": {"runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.32707"}}}, "Microsoft.Windows.Compatibility/8.0.10": {"dependencies": {"Microsoft.Win32.Registry.AccessControl": "8.0.0", "Microsoft.Win32.SystemEvents": "9.0.0-preview.6.24327.7", "System.CodeDom": "8.0.0", "System.ComponentModel.Composition": "8.0.0", "System.ComponentModel.Composition.Registration": "8.0.0", "System.Configuration.ConfigurationManager": "8.0.1", "System.Data.Odbc": "8.0.1", "System.Data.OleDb": "8.0.1", "System.Data.SqlClient": "4.8.6", "System.Diagnostics.EventLog": "8.0.1", "System.Diagnostics.PerformanceCounter": "8.0.1", "System.DirectoryServices": "8.0.0", "System.DirectoryServices.AccountManagement": "8.0.1", "System.DirectoryServices.Protocols": "8.0.0", "System.Drawing.Common": "9.0.0-preview.6.24327.6", "System.IO.Packaging": "8.0.1", "System.IO.Ports": "8.0.0", "System.Management": "8.0.0", "System.Reflection.Context": "8.0.0", "System.Runtime.Caching": "8.0.1", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Security.Cryptography.ProtectedData": "8.0.0", "System.Security.Cryptography.Xml": "8.0.2", "System.Security.Permissions": "8.0.0", "System.ServiceModel.Duplex": "4.10.3", "System.ServiceModel.Http": "4.10.3", "System.ServiceModel.NetTcp": "4.10.3", "System.ServiceModel.Primitives": "4.10.3", "System.ServiceModel.Security": "4.10.3", "System.ServiceModel.Syndication": "8.0.0", "System.ServiceProcess.ServiceController": "8.0.1", "System.Speech": "8.0.0", "System.Text.Encoding.CodePages": "8.0.0", "System.Threading.AccessControl": "9.0.0-preview.6.24327.7", "System.Web.Services.Description": "4.10.3"}}, "Microsoft.WSMan.Management/7.4.6": {"dependencies": {"Microsoft.WSMan.Runtime": "7.4.6", "System.Management.Automation": "7.4.6", "System.ServiceProcess.ServiceController": "8.0.1"}, "runtime": {"runtimes/win/lib/net8.0/Microsoft.WSMan.Management.dll": {"assemblyVersion": "7.4.6.500", "fileVersion": "7.4.6.500"}}}, "Microsoft.WSMan.Runtime/7.4.6": {"runtime": {"runtimes/win/lib/net8.0/Microsoft.WSMan.Runtime.dll": {"assemblyVersion": "7.4.6.500", "fileVersion": "7.4.6.500"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "OpenAI/2.2.0-beta.4": {"dependencies": {"System.ClientModel": "1.4.0-beta.1", "System.Diagnostics.DiagnosticSource": "8.0.1"}, "runtime": {"lib/net8.0/OpenAI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/8.0.0": {}, "runtime.linux-arm64.runtime.native.System.IO.Ports/8.0.0": {}, "runtime.linux-x64.runtime.native.System.IO.Ports/8.0.0": {}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.native.System.IO.Ports/8.0.0": {"dependencies": {"runtime.linux-arm.runtime.native.System.IO.Ports": "8.0.0", "runtime.linux-arm64.runtime.native.System.IO.Ports": "8.0.0", "runtime.linux-x64.runtime.native.System.IO.Ports": "8.0.0", "runtime.osx-arm64.runtime.native.System.IO.Ports": "8.0.0", "runtime.osx-x64.runtime.native.System.IO.Ports": "8.0.0"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/8.0.0": {}, "runtime.osx-x64.runtime.native.System.IO.Ports/8.0.0": {}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"native": {"runtimes/win-x64/native/sni.dll": {"fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {}, "System.ClientModel/1.4.0-beta.1": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.3", "System.Memory.Data": "8.0.1"}, "runtime": {"lib/net8.0/System.ClientModel.dll": {"assemblyVersion": "1.4.0.0", "fileVersion": "1.400.25.15605"}}}, "System.CodeDom/8.0.0": {"runtime": {"lib/net8.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Collections.Immutable/8.0.0": {}, "System.ComponentModel.Composition/8.0.0": {"runtime": {"lib/net8.0/System.ComponentModel.Composition.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.ComponentModel.Composition.Registration/8.0.0": {"dependencies": {"System.ComponentModel.Composition": "8.0.0", "System.Reflection.Context": "8.0.0"}, "runtime": {"lib/net8.0/System.ComponentModel.Composition.Registration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Configuration.ConfigurationManager/8.0.1": {"dependencies": {"System.Diagnostics.EventLog": "8.0.1", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Data.Odbc/8.0.1": {"runtime": {"runtimes/win/lib/net8.0/System.Data.Odbc.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Data.OleDb/8.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.1", "System.Diagnostics.PerformanceCounter": "8.0.1"}, "runtime": {"runtimes/win/lib/net8.0/System.Data.OleDb.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Data.SqlClient/4.8.6": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "5.0.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}}, "System.Diagnostics.DiagnosticSource/8.0.1": {}, "System.Diagnostics.EventLog/8.0.1": {"runtime": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Diagnostics.PerformanceCounter/8.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.1"}, "runtime": {"runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.DirectoryServices/8.0.0": {"runtime": {"runtimes/win/lib/net8.0/System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.DirectoryServices.AccountManagement/8.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.1", "System.DirectoryServices": "8.0.0", "System.DirectoryServices.Protocols": "8.0.0"}, "runtime": {"runtimes/win/lib/net8.0/System.DirectoryServices.AccountManagement.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.DirectoryServices.Protocols/8.0.0": {"runtime": {"runtimes/win/lib/net8.0/System.DirectoryServices.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Drawing.Common/9.0.0-preview.6.24327.6": {"dependencies": {"Microsoft.Win32.SystemEvents": "9.0.0-preview.6.24327.7"}, "runtime": {"lib/net8.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.32706"}, "lib/net8.0/System.Private.Windows.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.32706"}}}, "System.Formats.Asn1/8.0.1": {}, "System.IO.Packaging/8.0.1": {"runtime": {"lib/net8.0/System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.IO.Ports/8.0.0": {"dependencies": {"runtime.native.System.IO.Ports": "8.0.0"}, "runtime": {"runtimes/win/lib/net8.0/System.IO.Ports.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Management/8.0.0": {"dependencies": {"System.CodeDom": "8.0.0"}, "runtime": {"runtimes/win/lib/net8.0/System.Management.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Management.Automation/7.4.6": {"dependencies": {"Microsoft.ApplicationInsights": "2.21.0", "Microsoft.Management.Infrastructure": "3.0.0", "Microsoft.PowerShell.CoreCLR.Eventing": "7.4.6", "Microsoft.PowerShell.Native": "7.4.0", "Microsoft.Security.Extensions": "1.2.0", "Microsoft.Win32.Registry.AccessControl": "8.0.0", "Newtonsoft.Json": "13.0.3", "System.Configuration.ConfigurationManager": "8.0.1", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.DirectoryServices": "8.0.0", "System.Formats.Asn1": "8.0.1", "System.Management": "8.0.0", "System.Security.AccessControl": "6.0.1", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Security.Permissions": "8.0.0", "System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"runtimes/win/lib/net8.0/System.Management.Automation.dll": {"assemblyVersion": "7.4.6.500", "fileVersion": "7.4.6.500"}}}, "System.Memory/4.5.5": {}, "System.Memory.Data/8.0.1": {"runtime": {"lib/net8.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Net.Http.WinHttpHandler/8.0.2": {"runtime": {"runtimes/win/lib/net8.0/System.Net.Http.WinHttpHandler.dll": {"assemblyVersion": "8.0.0.2", "fileVersion": "8.0.824.36612"}}}, "System.Numerics.Tensors/9.0.6": {"runtime": {"lib/net8.0/System.Numerics.Tensors.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Private.ServiceModel/4.10.3": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.ObjectPool": "8.0.10", "System.Numerics.Vectors": "4.5.0", "System.Reflection.DispatchProxy": "4.7.1", "System.Security.Cryptography.Xml": "8.0.2", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Private.ServiceModel.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}, "resources": {"lib/netstandard2.0/cs/System.Private.ServiceModel.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.Private.ServiceModel.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.Private.ServiceModel.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.Private.ServiceModel.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.Private.ServiceModel.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.Private.ServiceModel.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.Private.ServiceModel.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.Private.ServiceModel.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.Private.ServiceModel.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.Private.ServiceModel.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.Private.ServiceModel.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.Private.ServiceModel.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.Private.ServiceModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Reflection.Context/8.0.0": {"runtime": {"lib/net8.0/System.Reflection.Context.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Reflection.DispatchProxy/4.7.1": {}, "System.Reflection.Metadata/8.0.0": {"dependencies": {"System.Collections.Immutable": "8.0.0"}}, "System.Runtime.Caching/8.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.1"}, "runtime": {"runtimes/win/lib/net8.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/6.0.1": {}, "System.Security.Cryptography.Pkcs/8.0.1": {"runtime": {"runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Security.Cryptography.ProtectedData/8.0.0": {"runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Security.Cryptography.Xml/8.0.2": {"dependencies": {"System.Security.Cryptography.Pkcs": "8.0.1"}, "runtime": {"lib/net8.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Security.Permissions/8.0.0": {"dependencies": {"System.Windows.Extensions": "8.0.0"}, "runtime": {"lib/net8.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.ServiceModel.Duplex/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Duplex.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.Http/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Http.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.NetTcp/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.NetTcp.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.Primitives/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Primitives.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}, "lib/net6.0/System.ServiceModel.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.Security/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Security.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.Syndication/8.0.0": {"runtime": {"lib/net8.0/System.ServiceModel.Syndication.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.ServiceProcess.ServiceController/8.0.1": {"dependencies": {"System.Diagnostics.EventLog": "8.0.1"}, "runtime": {"runtimes/win/lib/net8.0/System.ServiceProcess.ServiceController.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Speech/8.0.0": {"runtime": {"runtimes/win/lib/net8.0/System.Speech.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Text.Encoding.CodePages/8.0.0": {}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.5": {}, "System.Threading.AccessControl/9.0.0-preview.6.24327.7": {"runtime": {"runtimes/win/lib/net8.0/System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.32707"}}}, "System.Threading.Channels/8.0.0": {}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Web.Services.Description/4.10.3": {"runtime": {"lib/netstandard2.0/System.Web.Services.Description.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}, "resources": {"lib/netstandard2.0/cs/System.Web.Services.Description.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.Web.Services.Description.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.Web.Services.Description.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.Web.Services.Description.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.Web.Services.Description.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.Web.Services.Description.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.Web.Services.Description.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.Web.Services.Description.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.Web.Services.Description.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.Web.Services.Description.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.Web.Services.Description.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.Web.Services.Description.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.Web.Services.Description.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Windows.Extensions/8.0.0": {"runtime": {"runtimes/win/lib/net8.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}}}, "libraries": {"NetRagService/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Azure.AI.OpenAI/2.2.0-beta.4": {"type": "package", "serviceable": true, "sha512": "sha512-qjCgspdq67x+urifvf7Dkz4tX5HVU3AlF2XUYU/kQBObKQihPsTYSQJ4tiMHEMNjaKRbfHzxnE2vnuhcqUUWCg==", "path": "azure.ai.openai/2.2.0-beta.4", "hashPath": "azure.ai.openai.2.2.0-beta.4.nupkg.sha512"}, "Azure.Core/1.44.1": {"type": "package", "serviceable": true, "sha512": "sha512-YyznXLQZCregzHvioip07/BkzjuWNXogJEVz9T5W6TwjNr17ax41YGzYMptlo2G10oLCuVPoyva62y0SIRDixg==", "path": "azure.core/1.44.1", "hashPath": "azure.core.1.44.1.nupkg.sha512"}, "FastBertTokenizer/1.0.28": {"type": "package", "serviceable": true, "sha512": "sha512-7luZ+kHNzGesTIri8FfTEq6hLqPrMkd0Gq4KdFyytqDnqSueuQckxzqb7yYI6FOL9ucEwD7szMJAXDfcaywJ2A==", "path": "fastberttokenizer/1.0.28", "hashPath": "fastberttokenizer.1.0.28.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Json.More.Net/*******": {"type": "package", "serviceable": true, "sha512": "sha512-uF3QeiaXEfH92emz0/BWUiNtMSfxIIvgynuB0Bf1vF4s8eWTcZitBx9l+g/FDaJk5XxqBv9buQXizXKQcXFG1w==", "path": "json.more.net/*******", "hashPath": "json.more.net.*******.nupkg.sha512"}, "JsonPointer.Net/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fm4T5w20AY6C+p5/pJr0vrXRNGgtSfHl34I1LxC9zdPwS9S3j0GiR1Mz/CVPWKDXXGDpCt1APHpCq7kn5adCfA==", "path": "jsonpointer.net/5.0.0", "hashPath": "jsonpointer.net.5.0.0.nupkg.sha512"}, "JsonSchema.Net/7.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-R0Hk2Tr/np4Q1NO8CBjyQsoiD1iFJyEQP20Sw7JnZCNGJoaSBe+g4b+nZqnBXPQhiqY5LGZ8JZwZkRh/eKZhEQ==", "path": "jsonschema.net/7.0.4", "hashPath": "jsonschema.net.7.0.4.nupkg.sha512"}, "Markdig.Signed/0.33.0": {"type": "package", "serviceable": true, "sha512": "sha512-/BE/XANxmocgEqajbWB/ur4Jei+j1FkXppWH9JFmEuoq8T3xJndkQKZVCW/7lTdc9Ru6kfEAkwSXFOv30EkU2Q==", "path": "markdig.signed/0.33.0", "hashPath": "markdig.signed.0.33.0.nupkg.sha512"}, "Microsoft.ApplicationInsights/2.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-btZEDWAFNo9CoYliMCriSMTX3ruRGZTtYw4mo2XyyfLlowFicYVM2Xszi5evDG95QRYV7MbbH3D2RqVwfZlJHw==", "path": "microsoft.applicationinsights/2.21.0", "hashPath": "microsoft.applicationinsights.2.21.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.Bcl.HashCode/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MalY0Y/uM/LjXtHfX/26l2VtN4LDNZ2OE3aumNOHDLsT4fNYy2hiHXI4CXCqKpNUNm7iJ2brrc4J89UdaL56FA==", "path": "microsoft.bcl.hashcode/1.1.1", "hashPath": "microsoft.bcl.hashcode.1.1.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hashPath": "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-M5PThug7b2AdxL7xKmQs50KzAQTl9jENw5jMT3iUt16k+DAFlw1S87juU3UuPs3gvBm8trMBSOEvSFDr31c9Vw==", "path": "microsoft.codeanalysis.common/4.9.2", "hashPath": "microsoft.codeanalysis.common.4.9.2.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-HGIo7E9Mf3exAJbUdYpDFfLoYkSVaHDJXPyusWTYUTBaOPCowGw+Gap5McE1w+K+ryIXre72oiqL88sQHmHBmg==", "path": "microsoft.codeanalysis.csharp/4.9.2", "hashPath": "microsoft.codeanalysis.csharp.4.9.2.nupkg.sha512"}, "Microsoft.Extensions.AI/9.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-JrMdI7lKN23axyQpWLF2B1Pgzxo3+oO/1XNC90rlInlkdHnhOwqZ9vHlcZu5gZLtQPQLf6MbnWwgInm+GVuEpA==", "path": "microsoft.extensions.ai/9.6.0", "hashPath": "microsoft.extensions.ai.9.6.0.nupkg.sha512"}, "Microsoft.Extensions.AI.Abstractions/9.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-xGO7rHg3qK8jRdriAxIrsH4voNemCf8GVmgdcPXI5gpZ6lZWqOEM4ZO8yfYxUmg7+URw2AY1h7Uc/H17g7X1Kw==", "path": "microsoft.extensions.ai.abstractions/9.6.0", "hashPath": "microsoft.extensions.ai.abstractions.9.6.0.nupkg.sha512"}, "Microsoft.Extensions.AI.OpenAI/9.5.0-preview.1.25265.7": {"type": "package", "serviceable": true, "sha512": "sha512-htxD19JfZekY2vJSoMJn6lkBlZLcgMm7iK0MZc8pmuVT7FfNP7o+mTS4S0ZUDdBm1YR7NzZfYYjbwFOK/Z1gKg==", "path": "microsoft.extensions.ai.openai/9.5.0-preview.1.25265.7", "hashPath": "microsoft.extensions.ai.openai.9.5.0-preview.1.25265.7.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "path": "microsoft.extensions.configuration.commandline/8.0.0", "hashPath": "microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "path": "microsoft.extensions.configuration.environmentvariables/8.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "path": "microsoft.extensions.configuration.json/8.0.0", "hashPath": "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "path": "microsoft.extensions.configuration.usersecrets/8.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-BmANAnR5Xd4Oqw7yQ75xOAYODybZQRzdeNucg7kS5wWKd2PNnMdYtJ2Vciy0QLylRmv42DGl5+AFL9izA6F1Rw==", "path": "microsoft.extensions.dependencyinjection/8.0.1", "hashPath": "microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "path": "microsoft.extensions.diagnostics/8.0.0", "hashPath": "microsoft.extensions.diagnostics.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ItYHpdqVp5/oFLT5QqbopnkKlyFG9EW/9nhM6/yfObeKt6Su0wkBio6AizgRHGNwhJuAtlE5VIjow5JOTrip6w==", "path": "microsoft.extensions.hosting/8.0.0", "hashPath": "microsoft.extensions.hosting.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.WindowsServices/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-8JIvU6R9fejZj/p6QATeDaNEPtIvLuwE5Uh7qyPx7tp+Fc9FqYaAe6ylU2dM839IUpmsU4ZVev956slZyrYEhQ==", "path": "microsoft.extensions.hosting.windowsservices/8.0.0", "hashPath": "microsoft.extensions.hosting.windowsservices.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-dL0QGToTxggRLMYY4ZYX5AMwBb+byQBd/5dMiZE07Nv73o6I5Are3C7eQTh7K2+A4ct0PVISSr7TZANbiNb2yQ==", "path": "microsoft.extensions.logging.abstractions/8.0.3", "hashPath": "microsoft.extensions.logging.abstractions.8.0.3.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ixXXV0G/12g6MXK65TLngYN9V5hQQRuV+fZi882WIoVJT7h5JvoYoxTEwCgdqwLjSneqh1O+66gM8sMr9z/rsQ==", "path": "microsoft.extensions.logging.configuration/8.0.0", "hashPath": "microsoft.extensions.logging.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-e+48o7DztoYog+PY430lPxrM4mm3PbA6qucvQtUDDwVo4MO+ejMw7YGc/o2rnxbxj4isPxdfKFzTxvXMwAz83A==", "path": "microsoft.extensions.logging.console/8.0.0", "hashPath": "microsoft.extensions.logging.console.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dt0x21qBdudHLW/bjMJpkixv858RRr8eSomgVbU8qljOyfrfDGi1JQvpF9w8S7ziRPtRKisuWaOwFxJM82GxeA==", "path": "microsoft.extensions.logging.debug/8.0.0", "hashPath": "microsoft.extensions.logging.debug.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3X9D3sl7EmOu7vQp5MJrmIJBl5XSdOhZPYXUeFfYa6Nnm9+tok8x3t3IVPLhm7UJtPOU61ohFchw8rNm9tIYOQ==", "path": "microsoft.extensions.logging.eventlog/8.0.0", "hashPath": "microsoft.extensions.logging.eventlog.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oKcPMrw+luz2DUAKhwFXrmFikZWnyc8l2RKoQwqU3KIZZjcfoJE0zRHAnqATfhRZhtcbjl/QkiY2Xjxp0xu+6w==", "path": "microsoft.extensions.logging.eventsource/8.0.0", "hashPath": "microsoft.extensions.logging.eventsource.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-u7gAG7JgxF8VSJUGPSudAcPxOt+ymJKQCSxNRxiuKV+klCQbHljQR75SilpedCTfhPWDhtUwIJpnDVtspr9nMg==", "path": "microsoft.extensions.objectpool/8.0.10", "hashPath": "microsoft.extensions.objectpool.8.0.10.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.VectorData.Abstractions/9.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vth/omSCX2vR0JabzSRU/hdPhr0CvUVZlaS2lJPWHrEwvak8ntrQLDtLMtMiWKSvviGBe/WmjUW8gA3qqn9tjw==", "path": "microsoft.extensions.vectordata.abstractions/9.7.0", "hashPath": "microsoft.extensions.vectordata.abstractions.9.7.0.nupkg.sha512"}, "Microsoft.Management.Infrastructure/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cGZi0q5IujCTVYKo9h22Pw+UwfZDV82HXO8HTxMG2HqntPlT3Ls8jY6punLp4YzCypJNpfCAu2kae3TIyuAiJw==", "path": "microsoft.management.infrastructure/3.0.0", "hashPath": "microsoft.management.infrastructure.3.0.0.nupkg.sha512"}, "Microsoft.Management.Infrastructure.CimCmdlets/7.4.6": {"type": "package", "serviceable": true, "sha512": "sha512-pWlnAxpOLZf5lTX0SPX68dukSFowbEiGd39jRcO5rhsXDG5TjWrF3x1KxlFtMbiMGqJewVn5MJ7FlOWZDD7E3g==", "path": "microsoft.management.infrastructure.cimcmdlets/7.4.6", "hashPath": "microsoft.management.infrastructure.cimcmdlets.7.4.6.nupkg.sha512"}, "Microsoft.Management.Infrastructure.Runtime.Unix/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QZE3uEDvZ0m7LabQvcmNOYHp7v1QPBVMpB/ild0WEE8zqUVAP5y9rRI5we37ImI1bQmW5pZ+3HNC70POPm0jBQ==", "path": "microsoft.management.infrastructure.runtime.unix/3.0.0", "hashPath": "microsoft.management.infrastructure.runtime.unix.3.0.0.nupkg.sha512"}, "Microsoft.Management.Infrastructure.Runtime.Win/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwMyWN33+iQ8Wm/n1yoPXgFoiYNd0HzJyoqSVhaQZyJfaQrJR3udgcIHjqa1qbc3lS6kvfuUMN4TrF4U4refCQ==", "path": "microsoft.management.infrastructure.runtime.win/3.0.0", "hashPath": "microsoft.management.infrastructure.runtime.win.3.0.0.nupkg.sha512"}, "Microsoft.ML.OnnxRuntime/1.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-IC5jOaU6YZ7qcLl7JiR2yL6+NznthdCPwW8XgN2XkbvDERRFqMLIVtwfzu2H110fhms59VIyyMOxHXHl2iVzCg==", "path": "microsoft.ml.onnxruntime/1.22.0", "hashPath": "microsoft.ml.onnxruntime.1.22.0.nupkg.sha512"}, "Microsoft.ML.OnnxRuntime.Managed/1.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-zlG3eY5mJnx1BhYAxRwpuHCGHzl3B+cY5/se0RmlVBw6Yh6QTGjPAXdjhlBIcw6BPFhgMn9lxWPE/U3Fvis+BQ==", "path": "microsoft.ml.onnxruntime.managed/1.22.0", "hashPath": "microsoft.ml.onnxruntime.managed.1.22.0.nupkg.sha512"}, "Microsoft.ML.OnnxRuntimeGenAI/0.8.2": {"type": "package", "serviceable": true, "sha512": "sha512-DOFNGSmtGXt9w1T5710Rguf3vZt2oUaGi40teJK7D6QSByfs7c2xtqX2WmbMxWczIzI6ET1Qp66HvanvSk5zWQ==", "path": "microsoft.ml.onnxruntimegenai/0.8.2", "hashPath": "microsoft.ml.onnxruntimegenai.0.8.2.nupkg.sha512"}, "Microsoft.ML.OnnxRuntimeGenAI.Managed/0.8.2": {"type": "package", "serviceable": true, "sha512": "sha512-G5HL1thndfJXanw9Q4IHrHWgyFWnpHUWYb5zQnc2ifnh4fgK/FNgDbkfq60UVbVMpwI/NQUZwk2w0yBxuEALkQ==", "path": "microsoft.ml.onnxruntimegenai.managed/0.8.2", "hashPath": "microsoft.ml.onnxruntimegenai.managed.0.8.2.nupkg.sha512"}, "Microsoft.PowerShell.Commands.Diagnostics/7.4.6": {"type": "package", "serviceable": true, "sha512": "sha512-beys8wUfsWNda8JVr/Oj/Erx4PeC/AQC3X/TaU/O/BKKjzO1evm+Moz7wOOHb7bK3mqfGIWjsbOC9U/aRZGpGA==", "path": "microsoft.powershell.commands.diagnostics/7.4.6", "hashPath": "microsoft.powershell.commands.diagnostics.7.4.6.nupkg.sha512"}, "Microsoft.PowerShell.Commands.Management/7.4.6": {"type": "package", "serviceable": true, "sha512": "sha512-IqM/FMnJMWpausi+ONG/eD2w7phwHG6SgKtLOG7anmlCiFOefjnQX2Jf3ihm7TYQJIFbxg05LtoIviyICJ8Wrw==", "path": "microsoft.powershell.commands.management/7.4.6", "hashPath": "microsoft.powershell.commands.management.7.4.6.nupkg.sha512"}, "Microsoft.PowerShell.Commands.Utility/7.4.6": {"type": "package", "serviceable": true, "sha512": "sha512-dqu5AGtk/MH73osSK6on14X3a1TfBolKDnPMXM5EC6vo1XtpDL70MFoomypwgwkxz5B29p2+gtVoGqiQz5cicQ==", "path": "microsoft.powershell.commands.utility/7.4.6", "hashPath": "microsoft.powershell.commands.utility.7.4.6.nupkg.sha512"}, "Microsoft.PowerShell.ConsoleHost/7.4.6": {"type": "package", "serviceable": true, "sha512": "sha512-LOMd4Zl4rsf3gHHwDf0L8iFdvLgHBACMQlgS3viz0L49N2o+0v5TAJSLOpjg77lcuH43rKLfjN+aoEen78iUBQ==", "path": "microsoft.powershell.consolehost/7.4.6", "hashPath": "microsoft.powershell.consolehost.7.4.6.nupkg.sha512"}, "Microsoft.PowerShell.CoreCLR.Eventing/7.4.6": {"type": "package", "serviceable": true, "sha512": "sha512-z8XYESoGwJYT/L60192dE+n1dS7HRvbh2VinadhD3/zA6fDC/lu0VhQc42c56K4JiIMsLfHUUtaPJEa1JPaNBg==", "path": "microsoft.powershell.coreclr.eventing/7.4.6", "hashPath": "microsoft.powershell.coreclr.eventing.7.4.6.nupkg.sha512"}, "Microsoft.PowerShell.MarkdownRender/7.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-o5oUwL23R/KnjQPD2Oi49WAG5j4O4VLo1fPRSyM/aq0HuTrY2RnF4B3MCGk13BfcmK51p9kPlHZ1+8a/ZjO4Jg==", "path": "microsoft.powershell.markdownrender/7.2.1", "hashPath": "microsoft.powershell.markdownrender.7.2.1.nupkg.sha512"}, "Microsoft.PowerShell.Native/7.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-FlaJ3JBWhqFToYT0ycMb/Xxzoof7oTQbNyI4UikgubC7AMWt5ptBNKjIAMPvOcvEHr+ohaO9GvRWp3tiyS3sKw==", "path": "microsoft.powershell.native/7.4.0", "hashPath": "microsoft.powershell.native.7.4.0.nupkg.sha512"}, "Microsoft.PowerShell.SDK/7.4.6": {"type": "package", "serviceable": true, "sha512": "sha512-fQb9dKdx+fD5vbJ5wjFngeJ1qSkJ91M3jy6bomVsrs1tLNnB06/8pm42Cv+6xhUVQGKV0JKMFy44+QjHl5puqQ==", "path": "microsoft.powershell.sdk/7.4.6", "hashPath": "microsoft.powershell.sdk.7.4.6.nupkg.sha512"}, "Microsoft.PowerShell.Security/7.4.6": {"type": "package", "serviceable": true, "sha512": "sha512-62WzHhtzItjTMbEV88J5zL65/1HIZspEoWGwFAAi9ajoUzJOZUtdGyBQPa7ZsxszPk/Ktk+h3QtW0hwBVdrrPg==", "path": "microsoft.powershell.security/7.4.6", "hashPath": "microsoft.powershell.security.7.4.6.nupkg.sha512"}, "Microsoft.Security.Extensions/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-GjHZBE5PHKrxPRyGujWQKwbKNjPQYds6HcAWKeV49X3KPgBfF2B1vV5uJey5UluyGQlvAO/DezL7WzEx9HlPQA==", "path": "microsoft.security.extensions/1.2.0", "hashPath": "microsoft.security.extensions.1.2.0.nupkg.sha512"}, "Microsoft.SemanticKernel/1.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-zlioOIPIM7HdtIe/Dczg2rldGwqaSDpntf/z5wCiJWZLx9+ijgrODqakZYY5bHpyX9NlCAqSqr98UinncYEOMQ==", "path": "microsoft.semantickernel/1.60.0", "hashPath": "microsoft.semantickernel.1.60.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Abstractions/1.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-//jUQGgpWHf3Q9cNCsa259/j2FnSNQFrnW3fLgdEZ+aRC/C727j75GjwVlAhbsDd0K2+p+x3/bEM9jVHcNrKlw==", "path": "microsoft.semantickernel.abstractions/1.60.0", "hashPath": "microsoft.semantickernel.abstractions.1.60.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Connectors.AzureOpenAI/1.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-eV9svk8LepzfN5UTgEfLYV3NReH+8oYBGlQBBextZ2w1VQnxDyV6eq3PPyuoyr0HZ7K5sUPARicXE7cQuQS1EA==", "path": "microsoft.semantickernel.connectors.azureopenai/1.60.0", "hashPath": "microsoft.semantickernel.connectors.azureopenai.1.60.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Connectors.Onnx/1.60.0-alpha": {"type": "package", "serviceable": true, "sha512": "sha512-zpIxffK/2b578RqPsaAXgTsdJGZ6WoJQ7CVSNj03Qvf7ZMYI7S+k4+7oVrPp9jXf0tvD22tO2BzR2vc4RMI8zQ==", "path": "microsoft.semantickernel.connectors.onnx/1.60.0-alpha", "hashPath": "microsoft.semantickernel.connectors.onnx.1.60.0-alpha.nupkg.sha512"}, "Microsoft.SemanticKernel.Connectors.OpenAI/1.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-JtHBVzKkFDBfpzMExBvJj0nAVbBdauWrUSHFAdsvC3MJGZzRxl+glcun4tfZXKBfDWt0VXgWpklgFLdV3FzrEQ==", "path": "microsoft.semantickernel.connectors.openai/1.60.0", "hashPath": "microsoft.semantickernel.connectors.openai.1.60.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Core/1.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-dE3JkvQNKYN29mTg4Fu6iwf1Ao51jrbReudqEoQhGePcsDBsjQh7YVs6PM4zwUtZzdMeKfkEF4n39HQEz86oXw==", "path": "microsoft.semantickernel.core/1.60.0", "hashPath": "microsoft.semantickernel.core.1.60.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.Registry.AccessControl/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-u8PB9/v02C8mBXzl0vJ7bOyC020zOP+T1mRct+KA46DqZkB40XtsNn9pGD0QowTRsT6R4jPCghn+yAODn2UMMw==", "path": "microsoft.win32.registry.accesscontrol/8.0.0", "hashPath": "microsoft.win32.registry.accesscontrol.8.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/9.0.0-preview.6.24327.7": {"type": "package", "serviceable": true, "sha512": "sha512-iY1g4tiJLFzV+Ygp+g78w8sNMkDLiEY9nYnZVhVto+lAt6UVzw5ZqwRvLGDPzyrV9J1D/MKchmnziLkL8Ro6hA==", "path": "microsoft.win32.systemevents/9.0.0-preview.6.24327.7", "hashPath": "microsoft.win32.systemevents.9.0.0-preview.6.24327.7.nupkg.sha512"}, "Microsoft.Windows.Compatibility/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-V92Ri/nR0VqFT6vAVGj20sl0GI6tEFlZiB1IENyPdSdjs+1k5O1lr4vVwtIwoutlib8UyO8tnBwngT6SoOqvyA==", "path": "microsoft.windows.compatibility/8.0.10", "hashPath": "microsoft.windows.compatibility.8.0.10.nupkg.sha512"}, "Microsoft.WSMan.Management/7.4.6": {"type": "package", "serviceable": true, "sha512": "sha512-bBEx8+wstdxpsXjqkOPvVq53PwCksDWb7VtyzXjzLdMGY33gPo8H8P3OACUluOcMG/Kd7rPnBszsP7CFhSkKSg==", "path": "microsoft.wsman.management/7.4.6", "hashPath": "microsoft.wsman.management.7.4.6.nupkg.sha512"}, "Microsoft.WSMan.Runtime/7.4.6": {"type": "package", "serviceable": true, "sha512": "sha512-pnNVcVUT+CP7r23Ju/+nhp0fLSdwevAZwe3qe8XQEahYOUv9ACIP29GijRsjdOwIL8+7DaLUQF5jjh+P/ZjGTQ==", "path": "microsoft.wsman.runtime/7.4.6", "hashPath": "microsoft.wsman.runtime.7.4.6.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "OpenAI/2.2.0-beta.4": {"type": "package", "serviceable": true, "sha512": "sha512-JZ4/mlVXLaXDIZuC4Ddu0KCAA23z4Ax1AQTS26mpJRuSShjXik7DU8a3basY3ddD51W04F7jeX5eAXamKA6rHw==", "path": "openai/2.2.0-beta.4", "hashPath": "openai.2.2.0-beta.4.nupkg.sha512"}, "runtime.linux-arm.runtime.native.System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gK720fg6HemDg8sXcfy+xCMZ9+hF78Gc7BmREbmkS4noqlu1BAr9qZtuWGhLzFjBfgecmdtl4+SYVwJ1VneZBQ==", "path": "runtime.linux-arm.runtime.native.system.io.ports/8.0.0", "hashPath": "runtime.linux-arm.runtime.native.system.io.ports.8.0.0.nupkg.sha512"}, "runtime.linux-arm64.runtime.native.System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-KYG6/3ojhEWbb3FwQAKgGWPHrY+HKUXXdVjJlrtyCLn3EMcNTaNcPadb2c0ndQzixZSmAxZKopXJr0nLwhOrpQ==", "path": "runtime.linux-arm64.runtime.native.system.io.ports/8.0.0", "hashPath": "runtime.linux-arm64.runtime.native.system.io.ports.8.0.0.nupkg.sha512"}, "runtime.linux-x64.runtime.native.System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wnw5vhA4mgGbIFoo6l9Fk3iEcwRSq49a1aKwJgXUCUtEQLCSUDjTGSxqy/oMUuOyyn7uLHsH8KgZzQ1y3lReiQ==", "path": "runtime.linux-x64.runtime.native.system.io.ports/8.0.0", "hashPath": "runtime.linux-x64.runtime.native.system.io.ports.8.0.0.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.native.System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ee7Sz5llLpTgyKIWzKI/GeuRSbFkOABgJRY00SqTY0OkTYtkB+9l5rFZfE7fxPA3c22RfytCBYkUdAkcmwMjQg==", "path": "runtime.native.system.io.ports/8.0.0", "hashPath": "runtime.native.system.io.ports.8.0.0.nupkg.sha512"}, "runtime.osx-arm64.runtime.native.System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rbUBLAaFW9oVkbsb0+XSrAo2QdhBeAyzLl5KQ6Oci9L/u626uXGKInsVJG6B9Z5EO8bmplC8tsMiaHK8wOBZ+w==", "path": "runtime.osx-arm64.runtime.native.system.io.ports/8.0.0", "hashPath": "runtime.osx-arm64.runtime.native.system.io.ports.8.0.0.nupkg.sha512"}, "runtime.osx-x64.runtime.native.System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IcfB4jKtM9pkzP9OpYelEcUX1MiDt0IJPBh3XYYdEISFF+6Mc+T8WWi0dr9wVh1gtcdVjubVEIBgB8BHESlGfQ==", "path": "runtime.osx-x64.runtime.native.system.io.ports/8.0.0", "hashPath": "runtime.osx-x64.runtime.native.system.io.ports.8.0.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "System.ClientModel/1.4.0-beta.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZR0fKC94VS4P80vmxjk7l13/jPBXV0GMoE4jQfkYk8m2YV+dlw8jSC+b6eAfyBz0u+soN4CjhT3OdOC5KHaXxg==", "path": "system.clientmodel/1.4.0-beta.1", "hashPath": "system.clientmodel.1.4.0-beta.1.nupkg.sha512"}, "System.CodeDom/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WTlRjL6KWIMr/pAaq3rYqh0TJlzpouaQ/W1eelssHgtlwHAH25jXTkUphTYx9HaIIf7XA6qs/0+YhtLEQRkJ+Q==", "path": "system.codedom/8.0.0", "hashPath": "system.codedom.8.0.0.nupkg.sha512"}, "System.Collections.Immutable/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "path": "system.collections.immutable/8.0.0", "hashPath": "system.collections.immutable.8.0.0.nupkg.sha512"}, "System.ComponentModel.Composition/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bGhUX5BTivJ9Wax0qnJy7uGq7dn/TQkEpJ2Fpu1etg8dbPwyDkUzNPc1d3I2/jUr9y4wDI3a1dkSmi8X21Pzbw==", "path": "system.componentmodel.composition/8.0.0", "hashPath": "system.componentmodel.composition.8.0.0.nupkg.sha512"}, "System.ComponentModel.Composition.Registration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-BVMXYqX7Z0Zdq3tc94UKJL/cOWq4LF3ufexfdPuUDrDl4ekbbfwPVzsusVbx+aq6Yx60CJnmJLyHtM3V2Q7BBQ==", "path": "system.componentmodel.composition.registration/8.0.0", "hashPath": "system.componentmodel.composition.registration.8.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-gPYFPDyohW2gXNhdQRSjtmeS6FymL2crg4Sral1wtvEJ7DUqFCDWDVbbLobASbzxfic8U1hQEdC7hmg9LHncMw==", "path": "system.configuration.configurationmanager/8.0.1", "hashPath": "system.configuration.configurationmanager.8.0.1.nupkg.sha512"}, "System.Data.Odbc/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-JQd0QHOaZuH+ki+4Geas88dnLe/lZSaEYYmRdovZaqNVuExVlVFs/of2I1VaasMxzbO5+yrGDAP2rkazx/b8Sg==", "path": "system.data.odbc/8.0.1", "hashPath": "system.data.odbc.8.0.1.nupkg.sha512"}, "System.Data.OleDb/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-RO+/y2ggU5956uQDRXdjA1e2l5yJ4rTWNX76eZ+3sgtYGqGapCe2kQCyiUci+/y6Fyb21Irp4RQEdfrIiuYrxQ==", "path": "system.data.oledb/8.0.1", "hashPath": "system.data.oledb.8.0.1.nupkg.sha512"}, "System.Data.SqlClient/4.8.6": {"type": "package", "serviceable": true, "sha512": "sha512-2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "path": "system.data.sqlclient/4.8.6", "hashPath": "system.data.sqlclient.4.8.6.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vaoWjvkG1aenR2XdjaVivlCV9fADfgyhW5bZtXT23qaEea0lWiUljdQuze4E31vKM7ZWJaSUsbYIKE3rnzfZUg==", "path": "system.diagnostics.diagnosticsource/8.0.1", "hashPath": "system.diagnostics.diagnosticsource.8.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-n1ZP7NM2Gkn/MgD8+eOT5MulMj6wfeQMNS2Pizvq5GHCZfjlFMXV2irQlQmJhwA2VABC57M0auudO89Iu2uRLg==", "path": "system.diagnostics.eventlog/8.0.1", "hashPath": "system.diagnostics.eventlog.8.0.1.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-9RfEDiEjlUADeThs8IPdDVTXSnPRSqjfgTQJALpmGFPKC0k2mbdufOXnb/9JZ4I0TkmxOfy3VTJxrHOJSs8cXg==", "path": "system.diagnostics.performancecounter/8.0.1", "hashPath": "system.diagnostics.performancecounter.8.0.1.nupkg.sha512"}, "System.DirectoryServices/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7nit//efUTy1OsAKco2f02PMrwsR2S234N0dVVp84udC77YcvpOQDz5znAWMtgMWBzY1aRJvUW61jo/7vQRfXg==", "path": "system.directoryservices/8.0.0", "hashPath": "system.directoryservices.8.0.0.nupkg.sha512"}, "System.DirectoryServices.AccountManagement/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qVDWKClyDY+rHVEnf11eU4evW25d5OeidrtMPSJv+fwG213wa2zJ+AuIFCxsuvNSCFyHo+DvQIVfBcoK3CL1pA==", "path": "system.directoryservices.accountmanagement/8.0.1", "hashPath": "system.directoryservices.accountmanagement.8.0.1.nupkg.sha512"}, "System.DirectoryServices.Protocols/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-puwJxURHDrYLGTQdsHyeMS72ClTqYa4lDYz6LHSbkZEk5hq8H8JfsO4MyYhB5BMMxg93jsQzLUwrnCumj11UIg==", "path": "system.directoryservices.protocols/8.0.0", "hashPath": "system.directoryservices.protocols.8.0.0.nupkg.sha512"}, "System.Drawing.Common/9.0.0-preview.6.24327.6": {"type": "package", "serviceable": true, "sha512": "sha512-4mNJBnN4iroaz44NIcYVKSPgOqhBTsPHqBG5aTfSjsKWCkRzhKnkVsWuYzx4FrZoEToGveMvUCanlY/V7GUZ5A==", "path": "system.drawing.common/9.0.0-preview.6.24327.6", "hashPath": "system.drawing.common.9.0.0-preview.6.24327.6.nupkg.sha512"}, "System.Formats.Asn1/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqKba7Mm/koKSjKMfW82olQdmfbI5yqeoLV/tidRp7fbh5rmHAQ5raDI/7SU0swTzv+jgqtUGkzmFxuUg0it1A==", "path": "system.formats.asn1/8.0.1", "hashPath": "system.formats.asn1.8.0.1.nupkg.sha512"}, "System.IO.Packaging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KYkIOAvPexQOLDxPO2g0BVoWInnQhPpkFzRqvNrNrMhVT6kqhVr0zEb6KCHlptLFukxnZrjuMVAnxK7pOGUYrw==", "path": "system.io.packaging/8.0.1", "hashPath": "system.io.packaging.8.0.1.nupkg.sha512"}, "System.IO.Ports/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MaiPbx2/QXZc62gm/DrajRrGPG1lU4m08GWMoWiymPYM+ba4kfACp2PbiYpqJ4QiFGhHD00zX3RoVDTucjWe9g==", "path": "system.io.ports/8.0.0", "hashPath": "system.io.ports.8.0.0.nupkg.sha512"}, "System.Management/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jrK22i5LRzxZCfGb+tGmke2VH7oE0DvcDlJ1HAKYU8cPmD8XnpUT0bYn2Gy98GEhGjtfbR/sxKTVb+dE770pfA==", "path": "system.management/8.0.0", "hashPath": "system.management.8.0.0.nupkg.sha512"}, "System.Management.Automation/7.4.6": {"type": "package", "serviceable": true, "sha512": "sha512-RxrDUOJuLC7Sd2RaUHf52ujP5VpdfSVTv75nwgEVAROCfYV4IcSd5mL4mLrKa+6LjkXGli7aUs853UyCwidl1A==", "path": "system.management.automation/7.4.6", "hashPath": "system.management.automation.7.4.6.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Memory.Data/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-B<PERSON><PERSON>uec3jV23EMRDeR7Dr1/qhx7369dZzJ9IWy2xylvb4YfXsrUxspWc4UWYid/tj4zZK58uGZqn2WQiaDMhmAg==", "path": "system.memory.data/8.0.1", "hashPath": "system.memory.data.8.0.1.nupkg.sha512"}, "System.Net.Http.WinHttpHandler/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-PNtuWFl55FSigmCWX+Rj3h/1C5igGw3G4+cvnEe2kkwMDSWX08L/GuBw5S5Fc8R9PvOj+CRUHMY9w/Va8MKWHQ==", "path": "system.net.http.winhttphandler/8.0.2", "hashPath": "system.net.http.winhttphandler.8.0.2.nupkg.sha512"}, "System.Numerics.Tensors/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-NOLvZVal7jhuhmLFNuMQnCUclSAEvemJlwjyBxoa8CeK6Oj8326bM4AqB2dcH+8FGna3X3ZtP4PCLrIScyddtA==", "path": "system.numerics.tensors/9.0.6", "hashPath": "system.numerics.tensors.9.0.6.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Private.ServiceModel/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-BcUV7OERlLqGxDXZuIyIMMmk1PbqBblLRbAoigmzIUx/M8A+8epvyPyXRpbgoucKH7QmfYdQIev04Phx2Co08A==", "path": "system.private.servicemodel/4.10.3", "hashPath": "system.private.servicemodel.4.10.3.nupkg.sha512"}, "System.Reflection.Context/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-k76ubeIBOeIVg7vkQ4I+LoB8sY1EzFIc3oHEtoiNLhXleb7TBLXUQu0CFZ4sPlXJzWNabRf+gn1T7lyhOBxIMA==", "path": "system.reflection.context/8.0.0", "hashPath": "system.reflection.context.8.0.0.nupkg.sha512"}, "System.Reflection.DispatchProxy/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-C1sMLwIG6ILQ2bmOT4gh62V6oJlyF4BlHcVMrOoor49p0Ji2tA8QAoqyMcIhAdH6OHKJ8m7BU+r4LK2CUEOKqw==", "path": "system.reflection.dispatchproxy/4.7.1", "hashPath": "system.reflection.dispatchproxy.4.7.1.nupkg.sha512"}, "System.Reflection.Metadata/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ptvgrFh7PvWI8bcVqG5rsA/weWM09EnthFHR5SCnS6IN+P4mj6rE1lBDC4U8HL9/57htKAqy4KQ3bBj84cfYyQ==", "path": "system.reflection.metadata/8.0.0", "hashPath": "system.reflection.metadata.8.0.0.nupkg.sha512"}, "System.Runtime.Caching/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-tdl7Q47P09UpRu0C/OQsGJU6GacBzzk4vfp5My9rodD+BchrxmajORnTthH8RxPUTPrIoVDJmLyvJcGxB267nQ==", "path": "system.runtime.caching/8.0.1", "hashPath": "system.runtime.caching.8.0.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-IQ4NXP/B3Ayzvw0rDQzVTYsCKyy0Jp9KI6aYcK7UnGVlR9+Awz++TIPCQtPYfLJfOpm8ajowMR09V7quD3sEHw==", "path": "system.security.accesscontrol/6.0.1", "hashPath": "system.security.accesscontrol.6.0.1.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "path": "system.security.cryptography.pkcs/8.0.1", "hashPath": "system.security.cryptography.pkcs.8.0.1.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg==", "path": "system.security.cryptography.protecteddata/8.0.0", "hashPath": "system.security.cryptography.protecteddata.8.0.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-aDM/wm0ZGEZ6ZYJLzgqjp2FZdHbDHh6/OmpGfb7AdZ105zYmPn/83JRU2xLIbwgoNz9U1SLUTJN0v5th3qmvjA==", "path": "system.security.cryptography.xml/8.0.2", "hashPath": "system.security.cryptography.xml.8.0.2.nupkg.sha512"}, "System.Security.Permissions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-v/BBylw7XevuAsHXoX9dDUUfmBIcUf7Lkz8K3ZXIKz3YRKpw8YftpSir4n4e/jDTKFoaK37AsC3xnk+GNFI1Ow==", "path": "system.security.permissions/8.0.0", "hashPath": "system.security.permissions.8.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.ServiceModel.Duplex/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-IZ8ZahvTenWML7/jGUXSCm6jHlxpMbcb+Hy+h5p1WP9YVtb+Er7FHRRGizqQMINEdK6HhWpD6rzr5PdxNyusdg==", "path": "system.servicemodel.duplex/4.10.3", "hashPath": "system.servicemodel.duplex.4.10.3.nupkg.sha512"}, "System.ServiceModel.Http/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-hodkn0rPTYmoZ9EIPwcleUrOi1gZBPvU0uFvzmJbyxl1lIpVM5GxTrs/pCETStjOXCiXhBDoZQYajquOEfeW/w==", "path": "system.servicemodel.http/4.10.3", "hashPath": "system.servicemodel.http.4.10.3.nupkg.sha512"}, "System.ServiceModel.NetTcp/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-tP7GN7ehqSIQEz7yOJEtY8ziTpfavf2IQMPKa7r9KGQ75+uEW6/wSlWez7oKQwGYuAHbcGhpJvdG6WoVMKYgkw==", "path": "system.servicemodel.nettcp/4.10.3", "hashPath": "system.servicemodel.nettcp.4.10.3.nupkg.sha512"}, "System.ServiceModel.Primitives/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-aNcdry95wIP1J+/HcLQM/f/AA73LnBQDNc2uCoZ+c1//KpVRp8nMZv5ApMwK+eDNVdCK8G0NLInF+xG3mfQL+g==", "path": "system.servicemodel.primitives/4.10.3", "hashPath": "system.servicemodel.primitives.4.10.3.nupkg.sha512"}, "System.ServiceModel.Security/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-vqelKb7DvP2inb6LDJ5Igi8wpOYdtLXn5luDW5qEaqkV2sYO1pKlVYBpr6g6m5SevzbdZlVNu67dQiD/H6EdGQ==", "path": "system.servicemodel.security/4.10.3", "hashPath": "system.servicemodel.security.4.10.3.nupkg.sha512"}, "System.ServiceModel.Syndication/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CJxIUwpBkMCPmIx46tFVOt0zpRrYurUHLW6tJBcmyj+MyWpKc6MMcS69B7IdlV/bgtgys073wMIHZX9QOQ1OFA==", "path": "system.servicemodel.syndication/8.0.0", "hashPath": "system.servicemodel.syndication.8.0.0.nupkg.sha512"}, "System.ServiceProcess.ServiceController/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-02I0BXo1kmMBgw03E8Hu4K6nTqur4wpQdcDZrndczPzY2fEoGvlinE35AWbyzLZ2h2IksEZ6an4tVt3hi9j1oA==", "path": "system.serviceprocess.servicecontroller/8.0.1", "hashPath": "system.serviceprocess.servicecontroller.8.0.1.nupkg.sha512"}, "System.Speech/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CNuiA6vb95Oe5PRjClZEBiaju31vwB8OIeCgeSBXyZL6+MS4RVVB2X/C11z0xCkooHE3Vy91nM2z76emIzR+sg==", "path": "system.speech/8.0.0", "hashPath": "system.speech.8.0.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OZIsVplFGaVY90G2SbpgU7EnCoOO5pw1t4ic21dBF3/1omrJFpAGoNAVpPyMVOC90/hvgkGG3VFqR13YgZMQfg==", "path": "system.text.encoding.codepages/8.0.0", "hashPath": "system.text.encoding.codepages.8.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}, "System.Threading.AccessControl/9.0.0-preview.6.24327.7": {"type": "package", "serviceable": true, "sha512": "sha512-t7e5cLBMvBx9/YhNsCp8W8iUw7geh08y0GKFawfJUD5YLgx6AjO2D497+0qHbXRQGpl2uxBGmkWKnCZ5azILZQ==", "path": "system.threading.accesscontrol/9.0.0-preview.6.24327.7", "hashPath": "system.threading.accesscontrol.9.0.0-preview.6.24327.7.nupkg.sha512"}, "System.Threading.Channels/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CMaFr7v+57RW7uZfZkPExsPB6ljwzhjACWW1gfU35Y56rk72B/Wu+sTqxVmGSk4SFUlPc3cjeKND0zktziyjBA==", "path": "system.threading.channels/8.0.0", "hashPath": "system.threading.channels.8.0.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Web.Services.Description/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-ORCkTkUo9f1o4ACG+H6SV+0XSxVZ461w3cHzYxEU41y6aKWp1CeNTMYbtdxMw1we6c6t4Hqq15PdcLVcdqno/g==", "path": "system.web.services.description/4.10.3", "hashPath": "system.web.services.description.4.10.3.nupkg.sha512"}, "System.Windows.Extensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Obg3a90MkOw9mYKxrardLpY2u0axDMrSmy4JCdq2cYbelM2cUwmUir5Bomvd1yxmPL9h5LVHU1tuKBZpUjfASg==", "path": "system.windows.extensions/8.0.0", "hashPath": "system.windows.extensions.8.0.0.nupkg.sha512"}}}