/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    McpToolManager.cpp

Abstract:

    Implementation of the MCP Tool Manager component that manages MCP tool execution lifecycle.
    Handles tool parameters, validation, execution timeouts, and result formatting.
    Provides both synchronous and asynchronous execution capabilities.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/11/2025

--*/

#include "pch.hxx"
#include "McpToolManager.h"
#include "StringUtils.h"
#include "AimxConstants.h"
#include "McpToolManager.cpp.tmh"
#include <dsgetdc.h>
#include <lm.h>
#include <winhttp.h>

#pragma comment(lib, "netapi32.lib")
#pragma comment(lib, "winhttp.lib")
#include <dsgetdc.h>
#include <lm.h>

#pragma comment(lib, "netapi32.lib")

// Static member definitions
McpToolManager* McpToolManager::s_instance = nullptr;
std::shared_mutex McpToolManager::s_instanceMutex;

HRESULT
McpToolManager::Initialize()
/*++

Routine Description:
    Initialize the MCP Tool Manager component with configuration parameters.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    std::lock_guard<std::shared_mutex> lock(s_instanceMutex);
    
    TraceInfo(AimxMcpToolManager, "Initializing MCP Tool Manager");
    
    if (s_instance != nullptr)
    {
        TraceInfo(AimxMcpToolManager, "MCP Tool Manager already initialized");
        return AIMX_S_COMPONENT_ALREADY_INITIALIZED;
    }
    
    s_instance = new (std::nothrow) McpToolManager();
    if (s_instance == nullptr)
    {
        TraceErr(AimxMcpToolManager, "Failed to allocate MCP Tool Manager instance");
        return E_OUTOFMEMORY;
    }

    MCP_TOOL_EXECUTION_CONFIG toolConfig = {};
    toolConfig.maxConcurrentExecutions = AimxConstants::McpToolManager::AIMX_MCP_MAX_CONCURRENT_EXECUTIONS;
    toolConfig.defaultTimeoutMs = AimxConstants::McpToolManager::AIMX_MCP_DEFAULT_TIMEOUT_MS;
    toolConfig.enableRetry = true;
    toolConfig.retryCount = AimxConstants::McpToolManager::AIMX_MCP_DEFAULT_RETRY_COUNT;
    toolConfig.retryDelayMs = AimxConstants::McpToolManager::AIMX_MCP_RETRY_DELAY_MS;
    toolConfig.enableLogging = true;
    
    // Initialize configuration
    s_instance->m_config = toolConfig;
    s_instance->m_initialized = true;
    s_instance->m_totalExecutions = 0;
    s_instance->m_successfulExecutions = 0;
    s_instance->m_failedExecutions = 0;
    s_instance->m_timeoutExecutions = 0;

    // Initialize PowerShell server discovery
    s_instance->InitializePowerShellServerDiscovery();

    TraceInfo(AimxMcpToolManager, "MCP Tool Manager initialized successfully");
    return S_OK;
}

void
McpToolManager::Uninitialize()
/*++

Routine Description:
    Uninitialize the MCP Tool Manager component and cleanup resources.

Arguments:
    None.

Return Value:
    None.

--*/
{
    std::lock_guard<std::shared_mutex> lock(s_instanceMutex);
    
    TraceInfo(AimxMcpToolManager, "Uninitializing MCP Tool Manager");
    
    if (s_instance != nullptr)
    {
        // Cancel all pending async executions
        {
            std::unique_lock<std::shared_mutex> execLock(s_instance->m_executionMapMutex);
            for (auto& pair : s_instance->m_asyncExecutions)
            {
                auto& context = pair.second;
                if (context && context->hThread)
                {
                    context->cancelRequested = true;
                    WaitForSingleObject(context->hThread, 5000); // Wait up to 5 seconds
                    CloseHandle(context->hThread);
                }
            }
            s_instance->m_asyncExecutions.clear();
        }
        
        delete s_instance;
        s_instance = nullptr;
    }
    
    TraceInfo(AimxMcpToolManager, "MCP Tool Manager uninitialized");
}

McpToolManager::McpToolManager()
    : m_initialized(false)
    , m_totalExecutions(0)
    , m_successfulExecutions(0)
    , m_failedExecutions(0)
    , m_timeoutExecutions(0)
    , m_defaultPowerShellServer(L"")
{
    // Initialize default configuration
    m_config.defaultTimeoutMs = AimxConstants::McpToolManager::AIMX_MCP_DEFAULT_TIMEOUT_MS;
    m_config.maxConcurrentExecutions = AimxConstants::McpToolManager::AIMX_MCP_MAX_CONCURRENT_EXECUTIONS;
    m_config.enableRetry = true;
    m_config.retryCount = AimxConstants::McpToolManager::AIMX_MCP_DEFAULT_RETRY_COUNT;
    m_config.retryDelayMs = AimxConstants::McpToolManager::AIMX_MCP_RETRY_DELAY_MS;
    m_config.enableLogging = true;
}

McpToolManager::~McpToolManager()
{
    // Cleanup is handled in Uninitialize()
}

HRESULT
McpToolManager::ExecuteTool(
    _In_ const std::wstring& serverName,
    _In_ const std::wstring& toolName,
    _In_ const nlohmann::json& parameters,
    _Out_ MCP_TOOL_EXECUTION_RESULT& result
    )
/*++

Routine Description:
    Execute a tool synchronously with default timeout.

Arguments:
    serverName - Name of the MCP server containing the tool
    toolName - Name of the tool to execute
    parameters - Tool parameters as JSON
    result - Output execution result

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }
    
    return ExecuteToolWithTimeout(
        serverName,
        toolName,
        parameters,
        s_instance->m_config.defaultTimeoutMs,
        result
    );
}

HRESULT
McpToolManager::ExecuteToolWithTimeout(
    _In_ const std::wstring& serverName,
    _In_ const std::wstring& toolName,
    _In_ const nlohmann::json& parameters,
    _In_ DWORD timeoutMs,
    _Out_ MCP_TOOL_EXECUTION_RESULT& result
    )
/*++

Routine Description:
    Execute a tool synchronously with specified timeout.

Arguments:
    serverName - Name of the MCP server containing the tool
    toolName - Name of the tool to execute
    parameters - Tool parameters as JSON
    timeoutMs - Execution timeout in milliseconds
    result - Output execution result

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }
    
    TraceInfo(AimxMcpToolManager, "Executing tool synchronously: %ws::%ws", 
             serverName.c_str(), toolName.c_str());
    
    s_instance->m_totalExecutions++;
    
    HRESULT hr = s_instance->ExecuteToolInternal(
        serverName,
        toolName,
        parameters,
        timeoutMs,
        result
    );
    
    if (SUCCEEDED(hr))
    {
        s_instance->m_successfulExecutions++;
        TraceInfo(AimxMcpToolManager, "Tool execution completed successfully");
    }
    else
    {
        s_instance->m_failedExecutions++;
        TraceErr(AimxMcpToolManager, "Tool execution failed: %!HRESULT!", hr);
    }
    
    return hr;
}

HRESULT
McpToolManager::ExecuteToolInternal(
    _In_ const std::wstring& serverName,
    _In_ const std::wstring& toolName,
    _In_ const nlohmann::json& parameters,
    _In_ DWORD timeoutMs,
    _Out_ MCP_TOOL_EXECUTION_RESULT& result
    )
/*++

Routine Description:
    Internal method to execute a tool using MCP server manager.

Arguments:
    serverName - Name of the MCP server containing the tool
    toolName - Name of the tool to execute
    parameters - Tool parameters as JSON
    timeoutMs - Execution timeout in milliseconds
    result - Output execution result

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    UNREFERENCED_PARAMETER(timeoutMs);

    // Initialize result
    result.status = MCP_TOOL_EXECUTION_STATUS::RUNNING;
    result.result = nlohmann::json::object();
    result.errorMessage.clear();
    GetSystemTimeAsFileTime(&result.startTime);

    try
    {
        // Check if this is a PowerShell-based server that we can execute via HTTP
        if (serverName == L"ADPSMcpServer" || serverName == L"PSMcpServer" || serverName == L"PowerShellDirect")
        {
            TraceInfo(AimxMcpToolManager, "Using HTTP PowerShell execution for server: %ws", serverName.c_str());
            return ExecutePowerShellToolViaHttp(toolName, parameters, result);
        }

        // Validate execution request
        HRESULT hr = ValidateExecutionRequest(serverName, toolName, parameters);
        if (FAILED(hr))
        {
            TraceErr(AimxMcpToolManager, "Tool execution validation failed: %!HRESULT!", hr);
            return HandleExecutionError(hr, L"Validation failed", result);
        }

        // Find server by name
        MCP_SERVER_INFO serverInfo;
        hr = McpSvrMgr::GetServerInfoByName(serverName, serverInfo);
        if (FAILED(hr))
        {
            TraceErr(AimxMcpToolManager, "Failed to find server: %ws, hr: %!HRESULT!",
                     serverName.c_str(), hr);
            return HandleExecutionError(hr, L"Server not found", result);
        }

        // Execute tool via MCP server manager
        nlohmann::json toolResult;
        hr = McpSvrMgr::ExecuteServerTool(
            serverInfo.serverId,
            toolName,
            parameters,
            toolResult
        );

        if (FAILED(hr))
        {
            TraceErr(AimxMcpToolManager, "MCP tool execution failed: %!HRESULT!", hr);
            return HandleExecutionError(hr, L"MCP tool execution failed", result);
        }

        // Format result
        hr = FormatExecutionResult(toolResult, result);
        if (FAILED(hr))
        {
            TraceErr(AimxMcpToolManager, "Failed to format execution result: %!HRESULT!", hr);
            return HandleExecutionError(hr, L"Result formatting failed", result);
        }

        result.status = MCP_TOOL_EXECUTION_STATUS::COMPLETED;
        GetSystemTimeAsFileTime(&result.endTime);
        result.executionTimeMs = CalculateTimeDifferenceMs(
            result.startTime,
            result.endTime
        );

        TraceInfo(AimxMcpToolManager, "Tool execution completed successfully in %d ms",
                 result.executionTimeMs);

        return S_OK;
    }
    catch (...)
    {
        TraceErr(AimxMcpToolManager, "Exception during tool execution");
        return HandleExecutionError(E_FAIL, L"Unexpected exception", result);
    }
}

HRESULT
McpToolManager::ValidateExecutionRequest(
    _In_ const std::wstring& serverName,
    _In_ const std::wstring& toolName,
    _In_ const nlohmann::json& parameters
    )
/*++

Routine Description:
    Validate tool execution request parameters.

Arguments:
    serverName - Name of the MCP server
    toolName - Name of the tool
    parameters - Tool parameters

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (serverName.empty())
    {
        TraceErr(AimxMcpToolManager, "Server name is empty");
        return E_INVALIDARG;
    }

    if (toolName.empty())
    {
        TraceErr(AimxMcpToolManager, "Tool name is empty");
        return E_INVALIDARG;
    }

    // Validate JSON parameters
    if (parameters.is_null())
    {
        TraceWarn(AimxMcpToolManager, "Parameters are null - using empty object");
    }
    else if (!parameters.is_object())
    {
        TraceErr(AimxMcpToolManager, "Parameters must be a JSON object");
        return E_INVALIDARG;
    }

    return S_OK;
}

HRESULT
McpToolManager::FormatExecutionResult(
    _In_ const nlohmann::json& rawResult,
    _Out_ MCP_TOOL_EXECUTION_RESULT& formattedResult
    )
/*++

Routine Description:
    Format raw tool execution result into structured format.

Arguments:
    rawResult - Raw result from MCP tool execution
    formattedResult - Output formatted result

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    try
    {
        formattedResult.result = rawResult;

        // Check if result contains error information
        if (rawResult.contains("error"))
        {
            formattedResult.status = MCP_TOOL_EXECUTION_STATUS::FAILED;

            if (rawResult["error"].is_string())
            {
                formattedResult.errorMessage = Utf8ToWide(rawResult["error"].get<std::string>());
            }
            else if (rawResult["error"].is_object() && rawResult["error"].contains("message"))
            {
                formattedResult.errorMessage = Utf8ToWide(rawResult["error"]["message"].get<std::string>());
            }
            else
            {
                formattedResult.errorMessage = L"Unknown error occurred";
            }
        }
        else
        {
            formattedResult.status = MCP_TOOL_EXECUTION_STATUS::COMPLETED;
            formattedResult.errorMessage.clear();
        }

        return S_OK;
    }
    catch (...)
    {
        TraceErr(AimxMcpToolManager, "Exception formatting execution result");
        formattedResult.status = MCP_TOOL_EXECUTION_STATUS::FAILED;
        formattedResult.errorMessage = L"Failed to format execution result";
        return E_FAIL;
    }
}

HRESULT
McpToolManager::HandleExecutionError(
    _In_ HRESULT errorCode,
    _In_ const std::wstring& errorContext,
    _Out_ MCP_TOOL_EXECUTION_RESULT& result
    )
/*++

Routine Description:
    Handle tool execution errors and format error result.

Arguments:
    errorCode - The error HRESULT
    errorContext - Context description of the error
    result - Output result with error information

Return Value:
    The original error HRESULT.

--*/
{
    result.status = MCP_TOOL_EXECUTION_STATUS::FAILED;
    result.errorMessage = errorContext + L" (HRESULT: " + std::to_wstring(errorCode) + L")";
    result.result = nlohmann::json::object();
    result.result["error"] = WideToUtf8(result.errorMessage);

    GetSystemTimeAsFileTime(&result.endTime);
    result.executionTimeMs = CalculateTimeDifferenceMs(
        result.startTime,
        result.endTime
    );

    return errorCode;
}

// Tool identification and formatting utilities
std::wstring
McpToolManager::CreateToolId(
    _In_ const std::wstring& serverName,
    _In_ const std::wstring& toolName
    )
/*++

Routine Description:
    Create a unique tool identifier in the format "ServerName/ToolName".
    This provides a consistent way to identify tools across the system.

Arguments:
    serverName - Name of the MCP server
    toolName - Name of the tool

Return Value:
    Unique tool identifier string.

--*/
{
    return serverName + L"/" + toolName;
}

std::wstring
McpToolManager::CreateDisplayName(
    _In_ const std::wstring& serverName,
    _In_ const std::wstring& toolName
    )
/*++

Routine Description:
    Create a user-friendly display name in the format "ToolName (on ServerName)".
    This is used for user-facing messages and logs.

Arguments:
    serverName - Name of the MCP server
    toolName - Name of the tool

Return Value:
    User-friendly display name string.

--*/
{
    return toolName + L" (on " + serverName + L")";
}

bool
McpToolManager::ParseToolId(
    _In_ const std::wstring& toolId,
    _Out_ std::wstring& serverName,
    _Out_ std::wstring& toolName
    )
/*++

Routine Description:
    Parse a tool identifier back into server and tool names.
    Expects format "ServerName/ToolName".

Arguments:
    toolId - Tool identifier to parse
    serverName - Receives the server name
    toolName - Receives the tool name

Return Value:
    true if parsing succeeded, false otherwise.

--*/
{
    size_t pos = toolId.find(L"/");
    if (pos != std::wstring::npos && pos > 0 && pos < toolId.length() - 1)
    {
        serverName = toolId.substr(0, pos);
        toolName = toolId.substr(pos + 1);
        return true;
    }
    return false;
}

bool
McpToolManager::FindToolByName(
    _In_ const std::wstring& targetToolName,
    _In_ const std::vector<MCP_SERVER_INFO>& servers,
    _Out_ std::wstring& foundServerName,
    _Out_ MCP_TOOL_INFO& foundTool
    )
/*++

Routine Description:
    Find a tool by exact name match across all enabled servers.
    Returns the first matching tool found.

Arguments:
    targetToolName - Name of the tool to find
    servers - List of servers to search
    foundServerName - Receives the name of the server containing the tool
    foundTool - Receives the tool information

Return Value:
    true if tool was found, false otherwise.

--*/
{
    for (const auto& server : servers)
    {
        std::wstring serverNameWide = server.serverName.c_str();
        for (const auto& tool : server.availableTools)
        {
            if (tool.toolName == targetToolName)
            {
                foundServerName = serverNameWide;
                foundTool = tool;
                return true;
            }
        }
    }
    return false;
}

McpToolManager*
McpToolManager::GetInstance()
/*++

Routine Description:
    Get the singleton instance of the MCP Tool Manager.

Arguments:
    None.

Return Value:
    Pointer to the singleton instance, or nullptr if not initialized.

--*/
{
    std::shared_lock<std::shared_mutex> lock(s_instanceMutex);
    return s_instance;
}

HRESULT
McpToolManager::ExecutePowerShellToolDirect(
    _In_ const std::wstring& toolName,
    _In_ const nlohmann::json& parameters,
    _Out_ MCP_TOOL_EXECUTION_RESULT& result
    )
/*++

Routine Description:
    Execute a PowerShell tool directly using CreateProcess instead of MCP server.
    This bypasses the MCP protocol overhead for PowerShell-based tools.
    Enhanced to handle rich context from PowerShell command search results.

Arguments:
    toolName - Name of the PowerShell cmdlet/tool to execute
    parameters - JSON object containing tool parameters (may include full context)
    result - Output execution result

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxMcpToolManager, "Executing PowerShell tool directly: %ws", toolName.c_str());

    // Initialize result
    result.status = MCP_TOOL_EXECUTION_STATUS::RUNNING;
    result.result = nlohmann::json::object();
    result.errorMessage.clear();
    GetSystemTimeAsFileTime(&result.startTime);

    try
    {
        // Build PowerShell command with enhanced format for system session reliability
        std::wstring psCommand = L"powershell.exe";
        std::wstring psArgs = L"-NonInteractive -NoProfile -ExecutionPolicy Bypass -Command \"& { Import-Module ActiveDirectory; ";

        // Check if the LLM provided a complete command (enhanced workflow)
        if (parameters.contains("llm_generated_command") && parameters["llm_generated_command"].is_string())
        {
            // Use the LLM-generated command directly (this includes proper syntax and parameters)
            std::string llmCommand = parameters["llm_generated_command"].get<std::string>();
            std::wstring llmCommandWStr = Utf8ToWide(llmCommand);

            // Get server scope for the command
            std::wstring serverScope = GetPowerShellServerScope(llmCommandWStr);

            // Simply append the server scope (either empty string or " -server servername")
            llmCommandWStr += serverScope;

            psArgs += llmCommandWStr;

            // Add JSON conversion for robust data transfer
            psArgs += L" | ConvertTo-Json -Compress -Depth 5";

            TraceInfo(AimxMcpToolManager, "Using enhanced LLM-generated PowerShell command with server scope");
        }
        }

        psArgs += L" }\"";

        TraceInfo(AimxMcpToolManager, "Enhanced PowerShell command: %ws %ws", psCommand.c_str(), psArgs.c_str());

        // Create pipes for stdout/stderr
        SECURITY_ATTRIBUTES saAttr;
        saAttr.nLength = sizeof(SECURITY_ATTRIBUTES);
        saAttr.bInheritHandle = TRUE;
        saAttr.lpSecurityDescriptor = nullptr;

        HANDLE hChildStdOutRd = nullptr;
        HANDLE hChildStdOutWr = nullptr;
        HANDLE hChildStdErrRd = nullptr;
        HANDLE hChildStdErrWr = nullptr;

        if (!CreatePipe(&hChildStdOutRd, &hChildStdOutWr, &saAttr, 0))
        {
            TraceErr(AimxMcpToolManager, "Failed to create stdout pipe: %d", GetLastError());
            return HandleExecutionError(HRESULT_FROM_WIN32(GetLastError()), L"Failed to create stdout pipe", result);
        }

        if (!CreatePipe(&hChildStdErrRd, &hChildStdErrWr, &saAttr, 0))
        {
            TraceErr(AimxMcpToolManager, "Failed to create stderr pipe: %d", GetLastError());
            CloseHandle(hChildStdOutRd);
            CloseHandle(hChildStdOutWr);
            return HandleExecutionError(HRESULT_FROM_WIN32(GetLastError()), L"Failed to create stderr pipe", result);
        }

        // Ensure the read handles are not inherited
        SetHandleInformation(hChildStdOutRd, HANDLE_FLAG_INHERIT, 0);
        SetHandleInformation(hChildStdErrRd, HANDLE_FLAG_INHERIT, 0);

        // Create the child process
        PROCESS_INFORMATION piProcInfo;
        STARTUPINFO siStartInfo;
        ZeroMemory(&piProcInfo, sizeof(PROCESS_INFORMATION));
        ZeroMemory(&siStartInfo, sizeof(STARTUPINFO));

        siStartInfo.cb = sizeof(STARTUPINFO);
        siStartInfo.hStdError = hChildStdErrWr;
        siStartInfo.hStdOutput = hChildStdOutWr;
        siStartInfo.hStdInput = GetStdHandle(STD_INPUT_HANDLE);
        siStartInfo.dwFlags |= STARTF_USESTDHANDLES;

        // Build full command line
        std::wstring fullCommandLine = psCommand + L" " + psArgs;

        // Impersonate the current user before creating the process
        HANDLE hToken = nullptr;
        BOOL bImpersonating = FALSE;

        // Try to get the current thread token first (if already impersonating)
        if (OpenThreadToken(GetCurrentThread(), TOKEN_DUPLICATE | TOKEN_QUERY, TRUE, &hToken))
        {
            TraceInfo(AimxMcpToolManager, "Using existing thread token for impersonation");
        }
        // If no thread token, try to get the process token
        else if (OpenProcessToken(GetCurrentProcess(), TOKEN_DUPLICATE | TOKEN_QUERY, &hToken))
        {
            TraceInfo(AimxMcpToolManager, "Using process token for impersonation");
        }

        // If we have a token, duplicate it for impersonation
        HANDLE hImpersonationToken = nullptr;
        if (hToken != nullptr)
        {
            if (DuplicateToken(hToken, SecurityImpersonation, &hImpersonationToken))
            {
                if (ImpersonateLoggedOnUser(hImpersonationToken))
                {
                    bImpersonating = TRUE;
                    TraceInfo(AimxMcpToolManager, "Successfully impersonating user for PowerShell execution");
                }
                else
                {
                    TraceWarn(AimxMcpToolManager, "Failed to impersonate user: %d", GetLastError());
                }
            }
            else
            {
                TraceWarn(AimxMcpToolManager, "Failed to duplicate token: %d", GetLastError());
            }
            CloseHandle(hToken);
        }
        else
        {
            TraceWarn(AimxMcpToolManager, "Failed to get token for impersonation: %d", GetLastError());
        }

        BOOL bSuccess = CreateProcess(
            nullptr,                    // No module name (use command line)
            const_cast<LPWSTR>(fullCommandLine.c_str()), // Command line
            nullptr,                    // Process handle not inheritable
            nullptr,                    // Thread handle not inheritable
            TRUE,                       // Set handle inheritance to TRUE
            CREATE_NO_WINDOW,           // Creation flags
            nullptr,                    // Environment block
            nullptr,                    // Working directory
            &siStartInfo,               // Pointer to STARTUPINFO structure
            &piProcInfo                 // Pointer to PROCESS_INFORMATION structure
        );

        // Revert impersonation immediately after CreateProcess
        if (bImpersonating)
        {
            RevertToSelf();
            TraceInfo(AimxMcpToolManager, "Reverted impersonation after CreateProcess");
        }

        if (hImpersonationToken != nullptr)
        {
            CloseHandle(hImpersonationToken);
        }

        if (!bSuccess)
        {
            DWORD error = GetLastError();
            TraceErr(AimxMcpToolManager, "Failed to create PowerShell process: %d", error);

            // Ensure impersonation is reverted on error
            if (bImpersonating)
            {
                RevertToSelf();
                TraceInfo(AimxMcpToolManager, "Reverted impersonation after CreateProcess failure");
            }

            CloseHandle(hChildStdOutRd);
            CloseHandle(hChildStdOutWr);
            CloseHandle(hChildStdErrRd);
            CloseHandle(hChildStdErrWr);
            return HandleExecutionError(HRESULT_FROM_WIN32(error), L"Failed to create PowerShell process", result);
        }

        // Close write ends of pipes (child process owns them now)
        CloseHandle(hChildStdOutWr);
        CloseHandle(hChildStdErrWr);

        // Wait for process to complete (with timeout)
        DWORD waitResult = WaitForSingleObject(piProcInfo.hProcess, 120000); // 2 minute timeout for PowerShell startup and AD module loading

        if (waitResult == WAIT_TIMEOUT)
        {
            TraceErr(AimxMcpToolManager, "PowerShell process timed out");
            TerminateProcess(piProcInfo.hProcess, 1);
            CloseHandle(hChildStdOutRd);
            CloseHandle(hChildStdErrRd);
            CloseHandle(piProcInfo.hProcess);
            CloseHandle(piProcInfo.hThread);
            return HandleExecutionError(E_FAIL, L"PowerShell process timed out", result);
        }
        else if (waitResult != WAIT_OBJECT_0)
        {
            TraceErr(AimxMcpToolManager, "Failed to wait for PowerShell process: %d", GetLastError());
            CloseHandle(hChildStdOutRd);
            CloseHandle(hChildStdErrRd);
            CloseHandle(piProcInfo.hProcess);
            CloseHandle(piProcInfo.hThread);
            return HandleExecutionError(HRESULT_FROM_WIN32(GetLastError()), L"Failed to wait for PowerShell process", result);
        }

        // Get exit code
        DWORD exitCode;
        GetExitCodeProcess(piProcInfo.hProcess, &exitCode);

        // Read stdout
        std::string stdoutData;
        DWORD bytesRead;
        char buffer[4096];
        while (ReadFile(hChildStdOutRd, buffer, sizeof(buffer) - 1, &bytesRead, nullptr) && bytesRead > 0)
        {
            buffer[bytesRead] = '\0';
            stdoutData += buffer;
        }

        // Read stderr
        std::string stderrData;
        while (ReadFile(hChildStdErrRd, buffer, sizeof(buffer) - 1, &bytesRead, nullptr) && bytesRead > 0)
        {
            buffer[bytesRead] = '\0';
            stderrData += buffer;
        }

        // Cleanup handles
        CloseHandle(hChildStdOutRd);
        CloseHandle(hChildStdErrRd);
        CloseHandle(piProcInfo.hProcess);
        CloseHandle(piProcInfo.hThread);

        // Format result
        result.result = nlohmann::json::object();
        result.result["content"] = stdoutData;
        result.result["exitCode"] = exitCode;

        if (!stderrData.empty())
        {
            result.result["error"] = stderrData;
        }

        // Always treat PowerShell execution as successful - let the LLM interpret the results
        // Even if the cmdlet returns an error (like object not found), the execution itself succeeded
        result.status = MCP_TOOL_EXECUTION_STATUS::COMPLETED;

        if (exitCode == 0)
        {
            TraceInfo(AimxMcpToolManager, "PowerShell tool executed successfully with exit code 0");
        }
        else
        {
            TraceInfo(AimxMcpToolManager, "PowerShell tool executed successfully with exit code %d (cmdlet returned error, but execution completed)", exitCode);
        }

        GetSystemTimeAsFileTime(&result.endTime);
        result.executionTimeMs = CalculateTimeDifferenceMs(result.startTime, result.endTime);

        return S_OK;
    }
    catch (const std::exception& ex)
    {
        TraceErr(AimxMcpToolManager, "Exception during direct PowerShell execution: %s", ex.what());
        return HandleExecutionError(E_FAIL, L"Exception during direct PowerShell execution", result);
    }
    catch (...)
    {
        TraceErr(AimxMcpToolManager, "Unknown exception during direct PowerShell execution");
        return HandleExecutionError(E_FAIL, L"Unknown exception during direct PowerShell execution", result);
    }
}

HRESULT
McpToolManager::ExecutePowerShellToolViaHttp(
    _In_ const std::wstring& toolName,
    _In_ const nlohmann::json& parameters,
    _Out_ MCP_TOOL_EXECUTION_RESULT& result
    )
/*++

Routine Description:
    Execute a PowerShell tool via HTTP request to netragservice.
    This approach uses the C# hosted PowerShell runtime for better reliability.

Arguments:
    toolName - Name of the PowerShell cmdlet/tool to execute
    parameters - JSON object containing tool parameters
    result - Output execution result

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxMcpToolManager, "Executing PowerShell tool via HTTP: %ws", toolName.c_str());

    // Initialize result
    result.status = MCP_TOOL_EXECUTION_STATUS::RUNNING;
    result.result = nlohmann::json::object();
    result.errorMessage.clear();
    GetSystemTimeAsFileTime(&result.startTime);

    try
    {
        // Build PowerShell command
        std::wstring psCommand;

        // Check if the LLM provided a complete command (enhanced workflow)
        if (parameters.contains("llm_generated_command") && parameters["llm_generated_command"].is_string())
        {
            // Use the LLM-generated command directly
            std::string llmCommand = parameters["llm_generated_command"].get<std::string>();
            std::wstring llmCommandWStr = Utf8ToWide(llmCommand);

            // Get server scope for the command
            std::wstring serverScope = GetPowerShellServerScope(llmCommandWStr);

            // Simply append the server scope (either empty string or " -server servername")
            llmCommandWStr += serverScope;

            psCommand = llmCommandWStr;
            TraceInfo(AimxMcpToolManager, "Using LLM-generated PowerShell command with server scope");
        }
        else
        {
            // Fallback to basic command construction
            psCommand = toolName;

            // Add parameters from JSON
            if (!parameters.empty() && parameters.is_object())
            {
                for (auto& [key, value] : parameters.items())
                {
                    // Skip special fields
                    if (key == "llm_generated_command" || key == "full_context" || key == "command_examples")
                    {
                        continue;
                    }

                    psCommand += L" -" + std::wstring(key.begin(), key.end()) + L" ";

                    if (value.is_string())
                    {
                        std::string valueStr = value.get<std::string>();
                        std::wstring valueWStr(valueStr.begin(), valueStr.end());
                        psCommand += L"'" + valueWStr + L"'";
                    }
                    else if (value.is_number())
                    {
                        psCommand += std::to_wstring(value.get<double>());
                    }
                    else if (value.is_boolean())
                    {
                        psCommand += value.get<bool>() ? L"$true" : L"$false";
                    }
                    else
                    {
                        std::string valueStr = value.dump();
                        std::wstring valueWStr(valueStr.begin(), valueStr.end());
                        psCommand += L"'" + valueWStr + L"'";
                    }
                }
            }

            // Add server scope for basic commands
            std::wstring serverScope = GetPowerShellServerScope(psCommand);
            psCommand += serverScope;
        }

        // Create HTTP request JSON
        nlohmann::json httpRequest;
        httpRequest["command"] = WideToUtf8(psCommand);
        httpRequest["timeoutSeconds"] = 120;

        std::string requestBody = httpRequest.dump();

        TraceInfo(AimxMcpToolManager, "Sending HTTP request to netragservice for PowerShell execution");

        // Send HTTP POST request to netragservice
        HRESULT hr = SendHttpPowerShellRequest(requestBody, result);

        if (FAILED(hr))
        {
            TraceErr(AimxMcpToolManager, "HTTP PowerShell request failed: %!HRESULT!", hr);
            return HandleExecutionError(hr, L"HTTP PowerShell request failed", result);
        }

        GetSystemTimeAsFileTime(&result.endTime);
        result.executionTimeMs = CalculateTimeDifferenceMs(result.startTime, result.endTime);

        TraceInfo(AimxMcpToolManager, "HTTP PowerShell execution completed in %d ms", result.executionTimeMs);
        return S_OK;
    }
    catch (const std::exception& ex)
    {
        TraceErr(AimxMcpToolManager, "Exception during HTTP PowerShell execution: %s", ex.what());
        return HandleExecutionError(E_FAIL, L"Exception during HTTP PowerShell execution", result);
    }
    catch (...)
    {
        TraceErr(AimxMcpToolManager, "Unknown exception during HTTP PowerShell execution");
        return HandleExecutionError(E_FAIL, L"Unknown exception during HTTP PowerShell execution", result);
    }
}

std::wstring
McpToolManager::GetPowerShellServerScope(
    _In_ const std::wstring& command
    )
/*++

Routine Description:
    Get the appropriate server scope for PowerShell commands to prevent timeouts
    when running in system session. Checks if -Server parameter is already specified
    in the command, and if not, adds the cached default server.

Arguments:
    command - The PowerShell command to analyze

Return Value:
    Server name to use for the command, or empty string if -Server already specified.

--*/
{
    // Check if the command already contains a -Server parameter
    std::wstring lowerCommand = command;
    std::transform(lowerCommand.begin(), lowerCommand.end(), lowerCommand.begin(), ::towlower);

    // Look for -Server parameter (case insensitive)
    if (lowerCommand.find(L"-server") != std::wstring::npos)
    {
        TraceInfo(AimxMcpToolManager, "Command already contains -Server parameter, no modification needed");
        return L"";
    }

    // Return cached server (should already be initialized)
    std::shared_lock<std::shared_mutex> lock(m_serverScopeMutex);
    if (!m_defaultPowerShellServer.empty())
    {
        TraceInfo(AimxMcpToolManager, "Using cached default PowerShell server: %ws", m_defaultPowerShellServer.c_str());
        return L" -server " + m_defaultPowerShellServer;
    }

    // Fallback if somehow not initialized
    TraceWarn(AimxMcpToolManager, "PowerShell server not initialized, returning empty scope");
    return L"";
}

void
McpToolManager::InitializePowerShellServerDiscovery()
/*++

Routine Description:
    Initialize PowerShell server discovery during component initialization.
    This prevents timeouts during the first PowerShell command execution.

Arguments:
    None.

Return Value:
    None.

--*/
{
    std::unique_lock<std::shared_mutex> lock(m_serverScopeMutex);

    TraceInfo(AimxMcpToolManager, "Initializing PowerShell server discovery using Win32 APIs...");

    std::wstring discoveredServer;

    PDOMAIN_CONTROLLER_INFO pDomainControllerInfo = nullptr;
    DWORD dwResult = DsGetDcName(
        nullptr,                    // Computer name (nullptr for local)
        nullptr,                    // Domain name (nullptr for current domain)
        nullptr,                    // Domain GUID (nullptr)
        nullptr,                    // Site name (nullptr)
        DS_DIRECTORY_SERVICE_REQUIRED | DS_RETURN_DNS_NAME, // Flags
        &pDomainControllerInfo
    );

    if (dwResult == ERROR_SUCCESS && pDomainControllerInfo != nullptr)
    {
        discoveredServer = pDomainControllerInfo->DomainControllerName;
        // Remove leading backslashes if present
        if (discoveredServer.length() > 2 && discoveredServer.substr(0, 2) == L"\\\\")
        {
            discoveredServer = discoveredServer.substr(2);
        }
        TraceInfo(AimxMcpToolManager, "Found domain controller via DsGetDcName: %ws", discoveredServer.c_str());
        NetApiBufferFree(pDomainControllerInfo);
    }
    else
    {
        TraceWarn(AimxMcpToolManager, "DsGetDcName failed with error: %d, using localhost as fallback", dwResult);
        discoveredServer = L"localhost";
    }

    m_defaultPowerShellServer = discoveredServer;
    TraceInfo(AimxMcpToolManager, "Cached default PowerShell server during initialization: %ws", m_defaultPowerShellServer.c_str());
}

HRESULT
McpToolManager::SendHttpPowerShellRequest(
    _In_ const std::string& requestBody,
    _Out_ MCP_TOOL_EXECUTION_RESULT& result
    )
/*++

Routine Description:
    Send HTTP POST request to netragservice for PowerShell execution.

Arguments:
    requestBody - JSON request body
    result - Output execution result

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxMcpToolManager, "Sending HTTP request to netragservice");

    try
    {
        // Initialize WinHTTP
        HINTERNET hSession = WinHttpOpen(
            L"AIMX PowerShell Client/1.0",
            WINHTTP_ACCESS_TYPE_DEFAULT_PROXY,
            WINHTTP_NO_PROXY_NAME,
            WINHTTP_NO_PROXY_BYPASS,
            0
        );

        if (!hSession)
        {
            DWORD error = GetLastError();
            TraceErr(AimxMcpToolManager, "WinHttpOpen failed: %d", error);
            return HRESULT_FROM_WIN32(error);
        }

        // Connect to netragservice (default port 5000)
        HINTERNET hConnect = WinHttpConnect(
            hSession,
            L"localhost",
            5000,
            0
        );

        if (!hConnect)
        {
            DWORD error = GetLastError();
            TraceErr(AimxMcpToolManager, "WinHttpConnect failed: %d", error);
            WinHttpCloseHandle(hSession);
            return HRESULT_FROM_WIN32(error);
        }

        // Create HTTP request
        HINTERNET hRequest = WinHttpOpenRequest(
            hConnect,
            L"POST",
            L"/api/PowerShellExecution/execute",
            nullptr,
            WINHTTP_NO_REFERER,
            WINHTTP_DEFAULT_ACCEPT_TYPES,
            0
        );

        if (!hRequest)
        {
            DWORD error = GetLastError();
            TraceErr(AimxMcpToolManager, "WinHttpOpenRequest failed: %d", error);
            WinHttpCloseHandle(hConnect);
            WinHttpCloseHandle(hSession);
            return HRESULT_FROM_WIN32(error);
        }

        // Set headers
        std::wstring headers = L"Content-Type: application/json\r\n";
        WinHttpAddRequestHeaders(
            hRequest,
            headers.c_str(),
            (DWORD)headers.length(),
            WINHTTP_ADDREQ_FLAG_ADD
        );

        // Send request
        BOOL bResult = WinHttpSendRequest(
            hRequest,
            WINHTTP_NO_ADDITIONAL_HEADERS,
            0,
            (LPVOID)requestBody.c_str(),
            (DWORD)requestBody.length(),
            (DWORD)requestBody.length(),
            0
        );

        if (!bResult)
        {
            DWORD error = GetLastError();
            TraceErr(AimxMcpToolManager, "WinHttpSendRequest failed: %d", error);
            WinHttpCloseHandle(hRequest);
            WinHttpCloseHandle(hConnect);
            WinHttpCloseHandle(hSession);
            return HRESULT_FROM_WIN32(error);
        }

        // Receive response
        bResult = WinHttpReceiveResponse(hRequest, nullptr);
        if (!bResult)
        {
            DWORD error = GetLastError();
            TraceErr(AimxMcpToolManager, "WinHttpReceiveResponse failed: %d", error);
            WinHttpCloseHandle(hRequest);
            WinHttpCloseHandle(hConnect);
            WinHttpCloseHandle(hSession);
            return HRESULT_FROM_WIN32(error);
        }

        // Check status code
        DWORD statusCode = 0;
        DWORD statusCodeSize = sizeof(statusCode);
        WinHttpQueryHeaders(
            hRequest,
            WINHTTP_QUERY_STATUS_CODE | WINHTTP_QUERY_FLAG_NUMBER,
            WINHTTP_HEADER_NAME_BY_INDEX,
            &statusCode,
            &statusCodeSize,
            WINHTTP_NO_HEADER_INDEX
        );

        // Read response body
        std::string responseBody;
        DWORD bytesAvailable = 0;

        do
        {
            if (!WinHttpQueryDataAvailable(hRequest, &bytesAvailable))
            {
                DWORD error = GetLastError();
                TraceErr(AimxMcpToolManager, "WinHttpQueryDataAvailable failed: %d", error);
                break;
            }

            if (bytesAvailable > 0)
            {
                std::vector<char> buffer(bytesAvailable + 1);
                DWORD bytesRead = 0;

                if (WinHttpReadData(hRequest, buffer.data(), bytesAvailable, &bytesRead))
                {
                    buffer[bytesRead] = '\0';
                    responseBody.append(buffer.data(), bytesRead);
                }
            }
        } while (bytesAvailable > 0);

        // Cleanup handles
        WinHttpCloseHandle(hRequest);
        WinHttpCloseHandle(hConnect);
        WinHttpCloseHandle(hSession);

        // Process response
        if (statusCode == 200)
        {
            // Parse JSON response
            nlohmann::json responseJson = nlohmann::json::parse(responseBody);

            result.result = responseJson;

            if (responseJson.contains("success") && responseJson["success"].get<bool>())
            {
                result.status = MCP_TOOL_EXECUTION_STATUS::COMPLETED;
                result.errorMessage.clear();
            }
            else
            {
                result.status = MCP_TOOL_EXECUTION_STATUS::FAILED;
                if (responseJson.contains("error"))
                {
                    result.errorMessage = Utf8ToWide(responseJson["error"].get<std::string>());
                }
                else
                {
                    result.errorMessage = L"PowerShell execution failed";
                }
            }

            TraceInfo(AimxMcpToolManager, "HTTP PowerShell request completed with status: %d", statusCode);
            return S_OK;
        }
        else
        {
            TraceErr(AimxMcpToolManager, "HTTP request failed with status code: %d", statusCode);
            result.status = MCP_TOOL_EXECUTION_STATUS::FAILED;
            result.errorMessage = L"HTTP request failed with status code: " + std::to_wstring(statusCode);
            result.result = nlohmann::json::object();
            result.result["error"] = "HTTP " + std::to_string(statusCode);
            result.result["response_body"] = responseBody;
            return E_FAIL;
        }
    }
    catch (const std::exception& ex)
    {
        TraceErr(AimxMcpToolManager, "Exception in HTTP PowerShell request: %s", ex.what());
        result.status = MCP_TOOL_EXECUTION_STATUS::FAILED;
        result.errorMessage = L"Exception in HTTP request: " + Utf8ToWide(ex.what());
        result.result = nlohmann::json::object();
        result.result["error"] = ex.what();
        return E_FAIL;
    }
}
